# ChatGLM API 项目结构说明

## 📁 目录结构

```
ChatGLMAPI/
├── 📂 src/                     # 核心源代码
│   ├── __init__.py            # 模块初始化文件
│   ├── main.py                # FastAPI 服务器主程序
│   ├── video_generator.py     # 视频生成核心逻辑
│   └── config_manager.py      # 配置管理模块
│
├── 📂 tools/                   # 工具和管理脚本
│   ├── __init__.py            # 模块初始化文件
│   ├── chatglm_cookies.py     # Cookie 管理工具
│   └── gui_settings.py        # GUI 设置界面
│
├── 📂 tests/                   # 测试工具
│   ├── __init__.py            # 模块初始化文件
│   ├── video.py               # Python API 测试工具
│   └── video.js               # JavaScript API 测试工具
│
├── 📂 config/                  # 配置和数据文件
│   ├── config.json            # 应用配置文件
│   ├── cookies.json           # Cookie 数据文件
│   └── 📂 assets/             # 资源文件
│       └── 1.jpg              # 测试图片
│
├── 📂 docs/                    # 文档
│   └── ChatGLM_API_完整使用指南.md  # 详细使用指南
│
├── 📂 docker/                  # Docker 相关文件
│   └── Dockerfile             # Docker 构建文件
│
├── 📄 README.md               # 项目介绍
├── 📄 requirements.txt        # Python 依赖列表
├── 📄 start_server.py         # 服务器启动脚本
├── 📄 项目结构说明.md         # 本文件
└── 📂 __pycache__/            # Python 缓存文件
```

## 🚀 快速启动

### 1. 获取 Cookie
```bash
python tools/chatglm_cookies.py --login --stealth-mode
```

### 2. 启动服务器
```bash
# 使用启动脚本（推荐）
python start_server.py --reload

# 或直接使用 uvicorn
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 测试 API
```bash
python tests/video.py --mode both
```

## 📋 文件功能说明

### 核心源代码 (src/)

- **main.py**: FastAPI 应用主程序，定义 API 路由和请求处理逻辑
- **video_generator.py**: 视频生成的核心逻辑，包含浏览器自动化和任务轮询
- **config_manager.py**: 配置文件管理，支持服务器、浏览器、路径等配置

### 工具脚本 (tools/)

- **chatglm_cookies.py**: Cookie 管理工具，支持扫码登录、验证、导出等功能
- **gui_settings.py**: 图形化设置界面，提供可视化的配置管理

### 测试工具 (tests/)

- **video.py**: Python 版本的 API 测试工具，支持本地和网络图片测试
- **video.js**: JavaScript 版本的 API 测试工具，功能与 Python 版本一致

### 配置文件 (config/)

- **config.json**: 应用程序配置文件，包含服务器、浏览器、路径等设置
- **cookies.json**: ChatGLM 登录 Cookie 数据，用于维持登录状态
- **assets/**: 存放测试图片等资源文件

## 🔧 开发指南

### 添加新功能
1. 在 `src/` 目录下添加新的模块
2. 在 `src/__init__.py` 中导入新模块
3. 更新 `requirements.txt` 如果有新依赖

### 添加新工具
1. 在 `tools/` 目录下创建新脚本
2. 遵循现有的命令行参数格式
3. 添加相应的文档说明

### 添加新测试
1. 在 `tests/` 目录下创建测试文件
2. 使用统一的测试框架和格式
3. 确保测试可以独立运行

## 📦 部署说明

### Docker 部署
```bash
# 构建镜像
docker build -f docker/Dockerfile -t chatglm-api-proxy .

# 运行容器
docker run -d -p 8002:8000 \
  -v ./config/cookies.json:/app/config/cookies.json \
  --name chatglm-proxy \
  chatglm-api-proxy
```

### 本地部署
```bash
# 安装依赖
pip install -r requirements.txt
playwright install

# 获取 Cookie
python tools/chatglm_cookies.py --login --stealth-mode

# 启动服务
python start_server.py --reload
```

## 🛠️ 维护指南

### 定期维护任务
1. **更新 Cookie**: 定期验证和更新 `config/cookies.json`
2. **更新依赖**: 定期运行 `pip install -r requirements.txt --upgrade`
3. **检查选择器**: 如果网站更新，可能需要更新 `video_generator.py` 中的 CSS 选择器

### 故障排除
1. **Cookie 过期**: 重新运行 `python tools/chatglm_cookies.py --login --stealth-mode`
2. **依赖问题**: 重新安装依赖 `pip install -r requirements.txt --force-reinstall`
3. **浏览器问题**: 更新 Chrome 浏览器和 ChromeDriver

## 📚 相关文档

- [完整使用指南](./docs/ChatGLM_API_完整使用指南.md)
- [项目介绍](./README.md)

---

**注意**: 此项目结构遵循 Python 项目的最佳实践，便于维护和扩展。
