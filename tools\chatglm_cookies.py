#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatGLM Cookie 安全管理工具

功能特性：
- 安全扫码登录并导出 cookies（反检测技术）
- 验证现有 cookies 有效性
- 支持多种导出格式
- 命令行和编程接口
- 浏览器指纹随机化
- 人类行为模拟

安全增强：
- 使用 undetected-chromedriver 规避检测
- 随机化浏览器指纹和行为
- 智能重试机制

安装依赖：
pip install -r requirements.txt

使用方法：
python chatglm_cookies.py --login                    # 安全扫码登录
python chatglm_cookies.py --validate --file cookies.json   # 验证cookies
python chatglm_cookies.py --export --format py       # 导出为Python格式
python chatglm_cookies.py --login --stealth-mode     # 启用隐身模式
"""

import json
import time
import os
import argparse
import requests
import random
import threading
import math
from datetime import datetime, timedelta
import base64
import urllib.parse
from typing import Dict, List, Optional, Any
import hashlib
import platform

# 标准 Selenium 导入（核心功能）
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️ Selenium未安装，自动化登录功能不可用")

# 反检测 Selenium（可选增强）
try:
    import undetected_chromedriver as uc
    UC_AVAILABLE = True
except ImportError:
    UC_AVAILABLE = False
    print("⚠️ undetected-chromedriver未安装，将使用标准Selenium（功能略有限制）")

# 随机 User-Agent
try:
    from fake_useragent import UserAgent
    from random_user_agent.user_agent import UserAgent as RandomUA
    from random_user_agent.params import SoftwareName, OperatingSystem
    UA_AVAILABLE = True
except ImportError:
    UA_AVAILABLE = False
    print("⚠️ User-Agent随机化库未安装，将使用固定UA")

# 隐身模式支持
try:
    from selenium_stealth import stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    print("⚠️ selenium-stealth未安装，隐身模式不可用")

class BrowserFingerprintGenerator:
    """浏览器指纹随机化生成器"""
    
    def __init__(self):
        self.ua_generator = None
        if UA_AVAILABLE:
            try:
                self.ua_generator = UserAgent()
            except:
                self.ua_generator = None
    
    def generate_random_viewport(self) -> tuple:
        """生成随机视窗大小"""
        common_resolutions = [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024)
        ]
        return random.choice(common_resolutions)
    
    def generate_random_user_agent(self) -> str:
        """生成随机User-Agent"""
        if self.ua_generator:
            try:
                return self.ua_generator.chrome
            except:
                pass
        
        # 备用随机UA列表
        chrome_versions = ['120.0.0.0', '119.0.0.0', '118.0.0.0', '117.0.0.0']
        webkit_versions = ['537.36', '537.35', '537.34']
        
        version = random.choice(chrome_versions)
        webkit = random.choice(webkit_versions)
        
        return f'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{webkit} (KHTML, like Gecko) Chrome/{version} Safari/{webkit}'
    
    def generate_random_headers(self) -> Dict[str, str]:
        """生成随机请求头"""
        languages = [
            'zh-CN,zh;q=0.9,en;q=0.8',
            'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'zh-CN,zh;q=0.8,en;q=0.6',
        ]
        
        return {
            'User-Agent': self.generate_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': random.choice(languages),
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
    
    def generate_basic_chrome_options(self) -> List[str]:
        """生成基础Chrome启动选项（避免兼容性问题）"""
        width, height = self.generate_random_viewport()
        
        # 只使用最基础和兼容的选项
        basic_options = [
            f"--window-size={width},{height}",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-extensions",
            "--no-first-run",
            "--disable-default-apps",
            "--disable-popup-blocking",
            "--disable-infobars",
            "--disable-notifications"
        ]
        
        return basic_options
    
    def generate_stealth_chrome_options(self) -> List[str]:
        """生成隐身模式Chrome选项"""
        base_options = self.generate_basic_chrome_options()
        
        # 添加隐身选项（谨慎选择兼容的选项）
        stealth_options = [
            "--disable-blink-features=AutomationControlled",
            "--disable-plugins-discovery",
            "--disable-translate",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding"
        ]
        
        base_options.extend(stealth_options)
        return base_options

class HumanBehaviorSimulator:
    """增强版人类行为模拟器"""

    @staticmethod
    def random_delay(min_seconds: float = 0.5, max_seconds: float = 2.0):
        """随机延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    @staticmethod
    def generate_human_like_trajectory(start_x: int, start_y: int, end_x: int, end_y: int, steps: int = 20) -> list:
        """生成类人滑动轨迹"""
        trajectory = []

        # 计算基础路径
        dx = end_x - start_x
        dy = end_y - start_y

        for i in range(steps + 1):
            # 基础进度
            progress = i / steps

            # 添加贝塞尔曲线效果（更自然的轨迹）
            bezier_progress = 3 * progress * (1 - progress) ** 2 + 3 * progress ** 2 * (1 - progress) + progress ** 3

            # 计算当前位置
            current_x = start_x + dx * bezier_progress
            current_y = start_y + dy * bezier_progress

            # 添加随机抖动（模拟手部微颤）
            jitter_x = random.uniform(-2, 2) * (1 - abs(progress - 0.5) * 2)  # 中间抖动更大
            jitter_y = random.uniform(-2, 2) * (1 - abs(progress - 0.5) * 2)

            trajectory.append((
                int(current_x + jitter_x),
                int(current_y + jitter_y)
            ))

        return trajectory

    @staticmethod
    def simulate_human_slide(driver, start_element, end_x_offset: int = 300, duration: float = 2.0):
        """模拟人类滑动行为"""
        try:
            if not SELENIUM_AVAILABLE:
                print("🤖 Selenium不可用，跳过滑动模拟")
                return False

            actions = ActionChains(driver)

            # 获取起始元素位置
            start_rect = start_element.rect
            start_x = start_rect['x'] + start_rect['width'] // 2
            start_y = start_rect['y'] + start_rect['height'] // 2

            # 计算结束位置
            end_x = start_x + end_x_offset
            end_y = start_y + random.randint(-5, 5)  # 轻微垂直偏移

            print(f"🎯 开始人类化滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y})")

            # 生成轨迹
            trajectory = HumanBehaviorSimulator.generate_human_like_trajectory(
                start_x, start_y, end_x, end_y, steps=random.randint(15, 25)
            )

            # 移动到起始位置
            actions.move_to_element(start_element).perform()
            HumanBehaviorSimulator.random_delay(0.2, 0.5)

            # 按下鼠标
            actions.click_and_hold(start_element).perform()
            HumanBehaviorSimulator.random_delay(0.1, 0.3)

            # 执行轨迹移动
            step_duration = duration / len(trajectory)
            for i, (x, y) in enumerate(trajectory[1:], 1):
                # 计算相对移动距离
                prev_x, prev_y = trajectory[i-1]
                move_x = x - prev_x
                move_y = y - prev_y

                # 执行移动
                actions.move_by_offset(move_x, move_y).perform()

                # 动态延迟（开始快，中间慢，结束快）
                progress = i / len(trajectory)
                delay_factor = 1 + math.sin(progress * math.pi) * 0.5  # 中间更慢
                time.sleep(step_duration * delay_factor * random.uniform(0.8, 1.2))

            # 释放鼠标
            HumanBehaviorSimulator.random_delay(0.1, 0.3)
            actions.release().perform()

            print("✅ 人类化滑动完成")
            return True

        except Exception as e:
            print(f"❌ 滑动模拟失败: {e}")
            return False

    @staticmethod
    def simulate_mouse_movement(driver, actions=None):
        """模拟鼠标移动"""
        try:
            # 检查 ActionChains 是否可用
            if not SELENIUM_AVAILABLE:
                print("🤖 Selenium不可用，跳过鼠标模拟")
                return

            # 如果没有传入 actions，创建一个新的
            if actions is None:
                actions = ActionChains(driver)

            viewport_width = driver.execute_script("return window.innerWidth")
            viewport_height = driver.execute_script("return window.innerHeight")

            # 更自然的鼠标移动模式
            move_count = random.randint(3, 7)
            current_x, current_y = viewport_width // 2, viewport_height // 2

            for _ in range(move_count):
                # 生成目标位置
                target_x = random.randint(100, viewport_width - 100)
                target_y = random.randint(100, viewport_height - 100)

                # 生成轨迹
                trajectory = HumanBehaviorSimulator.generate_human_like_trajectory(
                    current_x, current_y, target_x, target_y, steps=random.randint(8, 15)
                )

                # 执行轨迹移动
                for i, (x, y) in enumerate(trajectory[1:], 1):
                    prev_x, prev_y = trajectory[i-1]
                    move_x = x - prev_x
                    move_y = y - prev_y
                    actions.move_by_offset(move_x, move_y).perform()
                    time.sleep(random.uniform(0.01, 0.05))

                current_x, current_y = target_x, target_y
                HumanBehaviorSimulator.random_delay(0.2, 0.8)

        except Exception as e:
            print(f"🤖 鼠标模拟出错: {e}")

    @staticmethod
    def simulate_reading_behavior(driver):
        """模拟阅读行为"""
        try:
            # 更自然的阅读模式
            scroll_patterns = [
                # 快速浏览模式
                [(100, 200), (150, 300), (80, 150)],
                # 仔细阅读模式
                [(50, 100), (60, 120), (40, 80), (70, 140)],
                # 跳跃式阅读
                [(200, 400), (100, 200), (300, 500)]
            ]

            pattern = random.choice(scroll_patterns)

            for scroll_range in pattern:
                scroll_y = random.randint(*scroll_range)

                # 分段滚动，更像人类
                segments = random.randint(2, 4)
                segment_scroll = scroll_y // segments

                for _ in range(segments):
                    driver.execute_script(f"window.scrollBy(0, {segment_scroll});")
                    HumanBehaviorSimulator.random_delay(0.3, 0.8)

                # 阅读停顿
                HumanBehaviorSimulator.random_delay(1.0, 2.5)

            # 滚动回顶部（分段进行）
            current_scroll = driver.execute_script("return window.pageYOffset;")
            if current_scroll > 0:
                segments = random.randint(3, 6)
                segment_scroll = current_scroll // segments

                for _ in range(segments):
                    driver.execute_script(f"window.scrollBy(0, -{segment_scroll});")
                    HumanBehaviorSimulator.random_delay(0.2, 0.5)

                # 确保回到顶部
                driver.execute_script("window.scrollTo(0, 0);")
                HumanBehaviorSimulator.random_delay(0.5, 1.0)

        except Exception as e:
            print(f"🤖 滚动模拟出错: {e}")

    @staticmethod
    def apply_advanced_anti_detection(driver):
        """应用高级反检测脚本"""
        try:
            print("🛡️ 应用高级反检测脚本...")

            # 反检测脚本集合
            anti_detection_scripts = [
                # 删除 webdriver 属性
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined});",

                # 修改 plugins 长度
                "Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});",

                # 修改 languages
                "Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']});",

                # 隐藏自动化标识
                "window.chrome = {runtime: {}};",

                # 修改 permissions
                """
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
                );
                """,

                # Canvas 指纹随机化
                """
                const getContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(type) {
                    if (type === '2d') {
                        const context = getContext.call(this, type);
                        const originalFillText = context.fillText;
                        context.fillText = function(text, x, y, maxWidth) {
                            const noise = Math.random() * 0.1;
                            return originalFillText.call(this, text, x + noise, y + noise, maxWidth);
                        };
                        return context;
                    }
                    return getContext.call(this, type);
                };
                """,

                # WebGL 指纹随机化
                """
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) {
                        return 'Intel Inc.';
                    }
                    if (parameter === 37446) {
                        return 'Intel Iris OpenGL Engine';
                    }
                    return getParameter.call(this, parameter);
                };
                """,

                # 时区随机化
                f"""
                const originalDateTimeFormat = Intl.DateTimeFormat;
                Intl.DateTimeFormat = function(...args) {{
                    if (args.length === 0) {{
                        args = ['zh-CN'];
                    }}
                    return originalDateTimeFormat.apply(this, args);
                }};
                """,

                # 屏幕分辨率随机化
                f"""
                Object.defineProperty(screen, 'width', {{get: () => {random.choice([1920, 1366, 1536, 1440])}}});
                Object.defineProperty(screen, 'height', {{get: () => {random.choice([1080, 768, 864, 900])}}});
                Object.defineProperty(screen, 'availWidth', {{get: () => screen.width}});
                Object.defineProperty(screen, 'availHeight', {{get: () => screen.height - 40}});
                """
            ]

            # 执行反检测脚本
            for script in anti_detection_scripts:
                try:
                    driver.execute_script(script)
                except Exception as e:
                    print(f"⚠️ 反检测脚本执行失败（可忽略）: {e}")

            print("✅ 高级反检测脚本应用完成")

        except Exception as e:
            print(f"⚠️ 应用反检测脚本失败: {e}")

class ChatGLMCookieManager:
    """ChatGLM 安全Cookie管理器"""
    
    def __init__(self, stealth_mode: bool = False):
        self.base_url = "https://chatglm.cn"
        self.login_url = "https://chatglm.cn/login"
        self.api_base = "https://chatglm.cn/chatglm"
        self.session = requests.Session()
        self.cookies = []
        self.stealth_mode = stealth_mode
        
        # 初始化组件
        self.fingerprint_gen = BrowserFingerprintGenerator()
        self.behavior_sim = HumanBehaviorSimulator()
        
        # 设置随机化请求头
        headers = self.fingerprint_gen.generate_random_headers()
        self.session.headers.update(headers)
        
        print(f"🔒 安全模式: {'启用' if stealth_mode else '关闭'}")
        print(f"👤 User-Agent: {headers['User-Agent'][:50]}...")
    
    def load_cookies_from_file(self, file_path: str = "cookies.json") -> bool:
        """从文件加载 cookies"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ Cookie文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()
            
            # 直接作为JSON加载
            self.cookies = json.loads(file_content)
            print(f"✅ 成功加载 {len(self.cookies)} 个cookies")
            
            # 验证cookie格式
            if not isinstance(self.cookies, list):
                print("❌ Cookie格式错误：应为列表格式")
                return False
            
            # 将cookies添加到session
            for cookie in self.cookies:
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    self.session.cookies.set(
                        name=cookie['name'],
                        value=cookie['value'],
                        domain=cookie.get('domain', '.chatglm.cn'),
                        path=cookie.get('path', '/')
                    )
            
            return True
            
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    def save_cookies_to_file(self, file_path: str = "cookies.json") -> bool:
        """保存 cookies 到文件"""
        try:
            # 明文保存
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.cookies, f, indent=2, ensure_ascii=False)
            print(f"✅ Cookies已保存到: {file_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存cookies失败: {e}")
            return False
    
    def validate_cookies(self) -> Dict[str, Any]:
        """验证 cookies 有效性"""
        result = {
            'valid': False,
            'expired_cookies': [],
            'missing_tokens': [],
            'user_info': None,
            'expires_in': None
        }
        
        # 检查必要的认证cookie
        required_cookies = ['chatglm_token']
        cookie_dict = {cookie['name']: cookie for cookie in self.cookies}
        
        for required in required_cookies:
            if required not in cookie_dict:
                result['missing_tokens'].append(required)
        
        if result['missing_tokens']:
            print(f"❌ 缺少必要的认证cookies: {result['missing_tokens']}")
            return result
        
        # 检查过期时间
        current_time = time.time()
        for cookie in self.cookies:
            if 'expires' in cookie and cookie['expires']:
                if current_time > cookie['expires']:
                    result['expired_cookies'].append(cookie['name'])
        
        if result['expired_cookies']:
            print(f"⚠️ 以下cookies已过期: {result['expired_cookies']}")
        
        # 尝试访问主页来验证有效性，检查是否会重定向到登录页
        try:
            response = self.session.get(
                self.base_url,
                timeout=20,
                allow_redirects=True # 允许重定向，以便检查最终URL
            )
            
            # 检查最终的URL是否为登录页面
            if response.status_code == 200 and "login" not in response.url.lower():
                result['valid'] = True
                print(f"✅ Cookies验证成功 (已访问主页)")
                
                # 计算token剩余时间
                token_cookie = cookie_dict.get('chatglm_token')
                if token_cookie and 'expires' in token_cookie and token_cookie['expires']:
                    expires_in = token_cookie['expires'] - current_time
                    result['expires_in'] = expires_in
                    if expires_in > 0:
                        days = int(expires_in // 86400)
                        hours = int((expires_in % 86400) // 3600)
                        print(f"🕐 Token剩余时间: {days}天 {hours}小时")
            else:
                print(f"❌ 验证失败: 访问主页后被重定向到登录页 (状态码: {response.status_code})")
                
        except requests.RequestException as e:
            print(f"❌ 验证请求失败: {e}")
        
        return result
    
    def scan_login(self, max_retries: int = 3) -> bool:
        """安全扫码登录方式（反检测）"""
        # 检查可用的WebDriver
        if not UC_AVAILABLE and not SELENIUM_AVAILABLE:
            print("❌ 无可用的WebDriver，无法使用登录功能")
            return False
        
        print("🚀 启动安全浏览器，准备扫码登录...")
        print(f"🔒 安全模式: {'启用' if self.stealth_mode else '标准模式'}")
        
        for attempt in range(max_retries):
            if attempt > 0:
                print(f"\n🔄 第 {attempt + 1} 次尝试...")
                self.behavior_sim.random_delay(2, 5)  # 重试前随机延迟
            
            try:
                driver = self._create_secure_driver()
                if not driver:
                    continue
                
                try:
                    # 安全访问登录页面
                    success = self._perform_secure_login(driver)
                    if success:
                        return True
                        
                except Exception as e:
                    print(f"❌ 登录过程出错: {e}")
                    if attempt < max_retries - 1:
                        print("🔄 准备重试...")
                    
                finally:
                    self._safe_close_driver(driver)
                    
            except Exception as e:
                print(f"❌ 浏览器启动失败: {e}")
                if attempt < max_retries - 1:
                    print("🔄 准备重试...")
        
        print("❌ 所有尝试均失败")
        return False
    
    def _create_secure_driver(self):
        """创建安全的WebDriver实例"""
        # 优先尝试undetected-chromedriver（如果启用隐身模式）
        if UC_AVAILABLE and self.stealth_mode:
            driver = self._try_undetected_chrome()
            if driver:
                return driver
            print("⚠️ undetected-chrome启动失败，回退到标准模式...")
        
        # 回退到标准Selenium
        if SELENIUM_AVAILABLE:
            driver = self._try_standard_chrome()
            if driver:
                return driver
        
        print("❌ 所有浏览器启动方式均失败")
        return None
    
    def _try_undetected_chrome(self):
        """尝试使用undetected-chromedriver"""
        try:
            print("🛡️ 启动反检测浏览器...")
            
            # 使用简化的Chrome选项
            basic_options = self.fingerprint_gen.generate_basic_chrome_options()
            
            # 创建UC选项对象
            uc_options = uc.ChromeOptions()
            
            # 添加基础选项
            for option in basic_options:
                uc_options.add_argument(option)
            
            # 反检测核心设置（使用实验性选项）
            uc_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            uc_options.add_experimental_option("useAutomationExtension", False)
            
            # 基础prefs设置
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 2
            }
            uc_options.add_experimental_option("prefs", prefs)
            
            # 尝试创建driver实例
            print("🔧 正在初始化undetected-chromedriver...")
            driver = uc.Chrome(options=uc_options, version_main=None)
            
            # 等待浏览器完全启动
            time.sleep(2)
            
            # 增强反检测脚本
            HumanBehaviorSimulator.apply_advanced_anti_detection(driver)

            # 应用stealth（如果可用）
            if STEALTH_AVAILABLE:
                try:
                    stealth(driver,
                        languages=["zh-CN", "zh"],
                        vendor="Google Inc.",
                        platform="Win32",
                        webgl_vendor="Intel Inc.",
                        renderer="Intel Iris OpenGL Engine",
                        fix_hairline=True,
                    )
                except Exception as e:
                    print(f"⚠️ stealth应用失败（可忽略）: {e}")
            
            print("✅ 反检测浏览器启动成功")
            return driver
            
        except Exception as e:
            print(f"❌ undetected-chrome启动失败: {e}")
            self._diagnose_chrome_issues(e)
            return None
    
    def _try_standard_chrome(self):
        """尝试使用标准Chrome"""
        try:
            print("🌐 启动标准浏览器...")
            
            chrome_options = Options()
            
            # 选择适当的选项
            if self.stealth_mode:
                options_list = self.fingerprint_gen.generate_stealth_chrome_options()
            else:
                options_list = self.fingerprint_gen.generate_basic_chrome_options()
            
            # 添加选项
            for option in options_list:
                chrome_options.add_argument(option)
            
            # 反检测设置（仅隐身模式）
            if self.stealth_mode:
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option("useAutomationExtension", False)
                
                # 基础prefs
                prefs = {
                    "profile.default_content_setting_values.notifications": 2,
                    "profile.default_content_settings.popups": 0
                }
                chrome_options.add_experimental_option("prefs", prefs)
            
            print("🔧 正在初始化Chrome WebDriver...")
            driver = webdriver.Chrome(options=chrome_options)
            
            # 等待浏览器启动
            time.sleep(1)
            
            # 反检测脚本
            if self.stealth_mode:
                HumanBehaviorSimulator.apply_advanced_anti_detection(driver)
            else:
                # 基础反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ 标准浏览器启动成功")
            return driver
            
        except Exception as e:
            print(f"❌ 标准Chrome启动失败: {e}")
            self._diagnose_chrome_issues(e)
            return None
    
    def _diagnose_chrome_issues(self, error):
        """诊断Chrome启动问题"""
        error_str = str(error).lower()
        
        print("\n🔍 Chrome启动问题诊断：")
        
        if "chromedriver" in error_str:
            print("❌ ChromeDriver问题：")
            print("   - 请确保已安装 Chrome 浏览器")
            print("   - 尝试更新 ChromeDriver：pip install --upgrade selenium")
            print("   - 或手动下载 ChromeDriver 并添加到PATH")
            
        elif "chrome option" in error_str or "unrecognized" in error_str:
            print("❌ Chrome选项兼容性问题：")
            print("   - Chrome版本可能过旧或过新")
            print("   - 尝试更新Chrome浏览器到最新版本")
            print("   - 或尝试不使用隐身模式：去掉 --stealth-mode 参数")
            
        elif "permission" in error_str or "access" in error_str:
            print("❌ 权限问题：")
            print("   - 以管理员身份运行终端")
            print("   - 检查防火墙设置")
            print("   - 确保Chrome未被其他进程占用")
            
        elif "timeout" in error_str:
            print("❌ 超时问题：")
            print("   - 网络连接可能较慢")
            print("   - 尝试关闭其他占用资源的程序")
            print("   - 重启计算机后重试")
            
        else:
            print("❌ 未知问题：")
            print("   - 尝试重启计算机")
            print("   - 确保Chrome浏览器正常运行")
            print("   - 检查系统防病毒软件设置")
        
        print("\n💡 建议解决方案：")
        print("   1. 先尝试不使用隐身模式：python chatglm_cookies.py --login")
        print("   2. 更新所有依赖：pip install -r requirements.txt --upgrade")
        print("   3. 重启系统后重试")
        print("   4. 如问题持续，请提供完整错误信息")
    
    def _perform_secure_login(self, driver) -> bool:
        """执行安全登录流程"""
        try:
            # 随机延迟开始
            self.behavior_sim.random_delay(1, 3)
            
            print("📄 正在打开 ChatGLM 登录页面...")
            driver.get(self.login_url)
            
            # 等待页面加载
            wait = WebDriverWait(driver, 15)
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # 模拟人类浏览行为
            print("🤖 模拟人类浏览行为...")
            self.behavior_sim.simulate_reading_behavior(driver)
            
            # 鼠标移动模拟
            self.behavior_sim.simulate_mouse_movement(driver)
            
            print("\n" + "="*60)
            print("📱 请在浏览器中完成扫码登录：")
            print("   1. 使用手机打开 ChatGLM APP")
            print("   2. 扫描页面上的二维码")
            print("   3. 确认登录")
            print("   4. 登录成功后，请回到终端按回车键继续...")
            print("="*60)
            
            # 等待用户完成登录
            input("\n✋ 登录完成后请按 [回车] 键继续...")
            
            # 验证登录状态
            return self._verify_login_success(driver)
            
        except TimeoutException:
            print("❌ 页面加载超时")
            return False
        except Exception as e:
            print(f"❌ 登录过程异常: {e}")
            return False
    
    def _verify_login_success(self, driver) -> bool:
        """验证登录成功并提取cookies"""
        try:
            print("\n🔍 检查登录状态...")
            
            # 添加随机延迟
            self.behavior_sim.random_delay(1, 2)
            
            current_url = driver.current_url
            login_success = False
            
            # 检查URL变化
            if "login" not in current_url.lower():
                login_success = True
                print("✅ 检测到URL已跳转，登录可能成功")
            else:
                # 检查页面元素
                try:
                    user_indicators = [
                        "[class*='user']", "[class*='avatar']", "[class*='profile']",
                        "[data-testid*='user']", ".user-menu", ".avatar"
                    ]
                    
                    for selector in user_indicators:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            login_success = True
                            print(f"✅ 检测到用户信息元素: {selector}")
                            break
                except:
                    pass
            
            if not login_success:
                print("⚠️ 无法确认登录状态，但将尝试获取cookies...")
            
            # 获取并处理cookies
            return self._extract_cookies(driver)
            
        except Exception as e:
            print(f"❌ 验证登录状态失败: {e}")
            return False
    
    def _extract_cookies(self, driver) -> bool:
        """提取并处理cookies"""
        try:
            print("🍪 正在提取cookies...")
            
            # 添加随机延迟
            self.behavior_sim.random_delay(0.5, 1.5)
            
            selenium_cookies = driver.get_cookies()
            
            if not selenium_cookies:
                print("❌ 未获取到任何cookies，登录可能失败")
                return False
            
            # 转换cookie格式
            self.cookies = []
            for cookie in selenium_cookies:
                cookie_obj = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie['domain'],
                    'path': cookie['path'],
                    'expires': cookie.get('expiry', time.time() + 86400 * 30),
                    'httpOnly': cookie.get('httpOnly', False),
                    'secure': cookie.get('secure', False),
                    'sameSite': cookie.get('sameSite', 'Lax')
                }
                self.cookies.append(cookie_obj)
            
            print(f"✅ 成功获取 {len(self.cookies)} 个cookies")
            
            # 检查认证相关cookies
            auth_patterns = ['token', 'session', 'auth', 'login', 'user']
            auth_cookies = []
            
            for cookie in self.cookies:
                for pattern in auth_patterns:
                    if pattern in cookie['name'].lower():
                        auth_cookies.append(cookie['name'])
                        break
            
            if auth_cookies:
                print(f"🔐 检测到 {len(auth_cookies)} 个认证相关的cookies: {', '.join(auth_cookies[:3])}{'...' if len(auth_cookies) > 3 else ''}")
                return True
            else:
                print("⚠️ 未检测到认证相关的cookies，请验证登录是否成功")
                return len(self.cookies) > 0  # 如果有任何cookies就认为可能成功
                
        except Exception as e:
            print(f"❌ 提取cookies失败: {e}")
            return False
    
    def _safe_close_driver(self, driver):
        """安全关闭浏览器"""
        if not driver:
            return
            
        try:
            print("\n🔒 正在关闭浏览器...")
            
            # 尝试优雅关闭
            try:
                driver.close()  # 关闭当前窗口
                time.sleep(0.5)
            except Exception:
                pass
            
            # 退出driver
            try:
                driver.quit()   # 退出整个浏览器进程
                time.sleep(1)
            except Exception:
                pass
                
            print("✅ 浏览器已安全关闭")
            
        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错（可忽略）: {e}")
        
        # 强制清理（如果需要）
        try:
            if hasattr(driver, 'service') and hasattr(driver.service, 'process'):
                if driver.service.process and driver.service.process.poll() is None:
                    driver.service.process.terminate()
        except Exception:
            pass
    
    def export_cookies(self, format_type: str = "json", file_path: str = None) -> str:
        """导出cookies为指定格式"""
        if not file_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"chatglm_cookies_{timestamp}.{format_type}"
        
        try:
            if format_type.lower() == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.cookies, f, indent=2, ensure_ascii=False)
            
            elif format_type.lower() == "txt":
                # Netscape cookies.txt 格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("# Netscape HTTP Cookie File\n")
                    for cookie in self.cookies:
                        secure = "TRUE" if cookie.get('secure', False) else "FALSE"
                        http_only = "TRUE" if cookie.get('httpOnly', False) else "FALSE"
                        expires = int(cookie.get('expires', time.time() + 86400 * 30))
                        
                        line = f"{cookie['domain']}\t{http_only}\t{cookie['path']}\t{secure}\t{expires}\t{cookie['name']}\t{cookie['value']}\n"
                        f.write(line)
            
            elif format_type.lower() == "py":
                # Python requests格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("# Python requests cookies\n")
                    f.write("cookies = {\n")
                    for cookie in self.cookies:
                        f.write(f"    '{cookie['name']}': '{cookie['value']}',\n")
                    f.write("}\n")
            
            print(f"✅ Cookies已导出到: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return None

def main():
    parser = argparse.ArgumentParser(description='ChatGLM Cookie 安全管理工具')
    
    # 操作类型
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--login', action='store_true', help='执行安全登录操作')
    action_group.add_argument('--validate', action='store_true', help='验证现有cookies')
    action_group.add_argument('--export', action='store_true', help='导出cookies')
    
    # 安全选项
    parser.add_argument('--stealth-mode', action='store_true', help='启用隐身模式（最高安全级别）')
    parser.add_argument('--retries', type=int, default=3, help='登录失败重试次数（默认3次）')
    
    # 文件参数
    parser.add_argument('--file', '-f', type=str, default='cookies.json', help='Cookie文件路径')
    parser.add_argument('--output', '-o', type=str, help='输出文件路径')
    parser.add_argument('--format', choices=['json', 'txt', 'py'], default='json', help='导出格式')
    
    args = parser.parse_args()
    
    # 创建管理器实例
    stealth_mode = args.stealth_mode
    
    print("=" * 60)
    print("ChatGLM Cookie 安全管理工具")
    print("=" * 60)
    
    # 显示安全设置
    if stealth_mode:
        print("🛡️ 隐身模式已启用 - 最高安全级别")
    if args.retries != 3:
        print(f"🔄 重试次数设置为: {args.retries}")
    
    manager = ChatGLMCookieManager(
        stealth_mode=stealth_mode
    )
    
    if args.login:
        print("🚀 开始安全扫码登录流程...")
        
        success = manager.scan_login(max_retries=args.retries)
            
        if success:
            # 保存cookies
            manager.save_cookies_to_file(args.file)
            print("🎉 登录成功！Cookies已安全保存")
            
            # 立即验证cookies有效性
            print("\n🔍 验证登录结果...")
            validation_result = manager.validate_cookies()
            if validation_result['valid']:
                print("✅ Cookie验证通过，登录确认成功")
            else:
                print("⚠️ Cookie验证失败，建议重新登录")
                return 1
        else:
            print("❌ 登录失败，请重试")
            return 1
    
    elif args.validate:
        print("🔍 验证cookies有效性...")
        
        if manager.load_cookies_from_file(args.file):
            result = manager.validate_cookies()
            
            if result['valid']:
                print("✅ Cookies有效")
                if result['user_info']:
                    print(f"👤 用户信息: {result['user_info']}")
            else:
                print("❌ Cookies无效或已过期")
                return 1
        else:
            return 1
    
    elif args.export:
        print("📤 导出cookies...")
        
        if manager.load_cookies_from_file(args.file):
            output_file = manager.export_cookies(args.format, args.output)
            if output_file:
                print(f"✅ 导出完成: {output_file}")
            else:
                print("❌ 导出失败")
                return 1
        else:
            return 1
    
    print("=" * 60)
    print("操作完成")
    return 0

if __name__ == "__main__":
    exit(main()) 