import uvicorn
import time
import uuid
import os
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any

# 从我们的自动化模块导入新的核心功能
from .video_generator import run_video_generation_flow

# --- OpenAI兼容模型定义 (已更新以支持多模态) ---

class Message(BaseModel):
    role: str
    # content字段现在可以是一个字符串，或一个包含文本和图片URL的字典列表
    content: Union[str, List[Dict[str, Any]]]

class ChatCompletionRequest(BaseModel):
    model: str = "glm-video"
    messages: List[Message]
    temperature: Optional[float] = 0.7
    stream: Optional[bool] = False
    # 视频生成专用参数
    aspect_ratio: Optional[str] = None  # 生成比例: "16:9", "9:16", "1:1", "4:3", "3:4"
    duration: Optional[str] = "5s"      # 生成时长: "5s" 或 "10s"
    ai_sound: Optional[bool] = False    # AI音效开关
    # 其他参数保持不变以确保兼容性

class Choice(BaseModel):
    index: int
    message: Message
    finish_reason: str = "stop"

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4()}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[Choice]

# --- FastAPI 应用实例 ---
app = FastAPI(
    title="ChatGLM Video Generation API Proxy",
    description="一个将智谱清言视频生成网页功能封装成OpenAI兼容API的代理服务器。",
    version="2.0.0" # 版本升级
)

# --- 异常处理器 (保持不变) ---
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "error": {"message": f"An internal server error occurred: {exc}", "type": "internal_server_error"}
        },
    )

# --- API 端点 (已更新) ---
@app.get("/", summary="服务状态检查")
def read_root():
    return {"status": "ChatGLM Video Generation API Proxy v2 is running."}

@app.post("/v1/chat/completions", response_model=ChatCompletionResponse, summary="OpenAI兼容的多模态完成端点")
async def create_chat_completion(request: ChatCompletionRequest):
    """
    这是核心API端点。它现在接收包含图片URL和文本的多模态请求，
    调用Playwright和HTTPX混合流程生成视频，然后返回视频URL。
    """
    print(f"\n{'='*60}")
    print(f"🎬 收到视频生成请求")
    print(f"{'='*60}")
    
    # 1. 从多模态请求中提取图片源和文本提示
    image_source = None
    prompt_text = None
    
    # 通常用户消息在最后一条
    user_message = next((msg for msg in reversed(request.messages) if msg.role == "user"), None)

    if user_message and isinstance(user_message.content, list):
        for part in user_message.content:
            if part.get("type") == "text":
                prompt_text = part.get("text")
                print(f"📝 提取到文本提示: {prompt_text}")
            elif part.get("type") == "image_url":
                image_source = part.get("image_url", {}).get("url")
                # 判断是本地还是网络图片
                if image_source.startswith("data:"):
                    print(f"🖼️ 检测到Base64编码的本地图片（长度: {len(image_source)} 字符）")
                else:
                    print(f"🌐 检测到网络图片URL: {image_source}")

    if not image_source or not prompt_text:
        print("❌ 请求格式错误：缺少图片或文本")
        raise HTTPException(
            status_code=400, 
            detail={"error": "Request must include one image_url and one text part in the user message content."}
        )
    
    # 验证参数
    if request.aspect_ratio and request.aspect_ratio not in ["16:9", "9:16", "1:1", "4:3", "3:4"]:
        raise HTTPException(
            status_code=400,
            detail={"error": "Invalid aspect_ratio. Must be one of: 16:9, 9:16, 1:1, 4:3, 3:4"}
        )

    if request.duration and request.duration not in ["5s", "10s"]:
        raise HTTPException(
            status_code=400,
            detail={"error": "Invalid duration. Must be either '5s' or '10s'"}
        )

    print(f"🚀 开始处理视频生成...")
    print(f"   模型: {request.model}")
    print(f"   提示词: {prompt_text}")
    print(f"   图片源: {'Base64本地图片' if image_source.startswith('data:') else image_source}")
    print(f"   生成比例: {request.aspect_ratio or '默认'}")
    print(f"   生成时长: {request.duration}")
    print(f"   AI音效: {'开启' if request.ai_sound else '关闭'}")

    # 2. 调用新的核心逻辑来生成视频
    try:
        # 查找cookies.json文件，支持多个可能的路径
        possible_paths = [
            "cookies.json",                    # 当前目录
            "../cookies.json",                 # 上级目录
            "config/cookies.json",             # config目录
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "cookies.json")  # 项目根目录
        ]

        cookies_file = None
        for path in possible_paths:
            if os.path.exists(path):
                cookies_file = path
                break

        if not cookies_file:
            print(f"⚠️ 警告: 'cookies.json' 未找到。将以未登录状态尝试，很可能失败。")
            print(f"🔍 已检查路径: {', '.join(possible_paths)}")
            cookies_file = "cookies.json"  # 使用默认路径作为备选
        else:
            print(f"🍪 找到Cookie文件: {cookies_file}")

        print(f"🎯 调用视频生成流程...")
        result_dict = await run_video_generation_flow(
            image_source=image_source,
            prompt=prompt_text,
            cookies_path=cookies_file,
            aspect_ratio=request.aspect_ratio,
            duration=request.duration,
            ai_sound=request.ai_sound
        )
        print(f"✅ 视频生成流程完成，结果: {result_dict}")
        
    except Exception as e:
        print(f"❌ 视频生成流程出现异常: {str(e)}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred in generation flow: {str(e)}")

    # 3. 根据返回结果构建响应
    if not result_dict.get("success"):
        error_message = result_dict.get('error', 'Unknown error during video generation.')
        print(f"❌ 视频生成失败: {error_message}")
        raise HTTPException(status_code=500, detail=error_message)

    video_url = result_dict.get("video_url")
    print(f"🎉 视频生成成功！")
    print(f"🔗 视频URL: {video_url}")
    
    response_message = Message(
        role="assistant",
        content=f"视频已为你生成！\n[点击这里查看或下载]({video_url})"
    )
    
    choice = Choice(
        index=0,
        message=response_message,
        finish_reason="stop"
    )

    response = ChatCompletionResponse(
        model=request.model,
        choices=[choice]
    )
    
    print(f"📤 返回响应给客户端")
    print(f"{'='*60}\n")
    
    return response

# if __name__ == "__main__":
#     print("启动FastAPI服务器 (v2)...")
#     # 加载配置
#     try:
#         from config_manager import get_config
#         config = get_config()
#         print(f"✅ 配置已加载")
#         print(f"🔧 服务器配置: {config.server.host}:{config.server.port}")
#         print(f"🌐 浏览器模式: {'有头' if not config.browser.headless else '无头'}")
#         uvicorn.run(app, host=config.server.host, port=config.server.port, reload=config.server.reload)
#     except ImportError:
#         print("⚠️ 配置管理器未找到，使用默认设置")
#         uvicorn.run(app, host="0.0.0.0", port=8002)

# =====================================================================================
# 推荐的启动方式:
# 在终端中运行以下命令来启动服务器，这将启用热重载功能：
#
# uvicorn main:app --host 0.0.0.0 --port 8002 --reload
#
# 你可以根据 config.json 中的配置修改 host 和 port。
# ===================================================================================== 