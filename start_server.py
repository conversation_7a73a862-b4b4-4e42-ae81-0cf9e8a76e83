#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatGLM API 服务器启动脚本

使用方法：
python start_server.py [--port 8000] [--host 0.0.0.0] [--reload]
"""

import argparse
import uvicorn
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    parser = argparse.ArgumentParser(description='ChatGLM API 服务器启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--reload', action='store_true', help='启用热重载（开发模式）')
    parser.add_argument('--workers', type=int, default=1, help='工作进程数量')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🚀 ChatGLM Video API 服务器")
    print("=" * 60)
    print(f"📡 服务地址: http://{args.host}:{args.port}")
    print(f"🔄 热重载: {'启用' if args.reload else '禁用'}")
    print(f"👥 工作进程: {args.workers}")
    print("=" * 60)
    
    # 检查必要文件
    config_file = "config/cookies.json"
    if not os.path.exists(config_file):
        print("⚠️ 警告: 未找到 cookies.json 文件")
        print("请先运行以下命令获取 Cookie:")
        print("python tools/chatglm_cookies.py --login --stealth-mode")
        print()
    
    try:
        uvicorn.run(
            "src.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
