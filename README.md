# ChatGLM 视频生成 API 代理

> 🎯 将智谱清言视频生成功能封装为 OpenAI 兼容 API

## 项目简介

本项目是一个中间层代理服务器，将智谱清言（chatglm.cn）网站的视频生成功能封装成与 OpenAI API 完全兼容的接口。支持任何兼容 OpenAI Vision API 的客户端无缝对接。

## 核心特性

- 🎯 **OpenAI Vision API 兼容**：完全模拟 `/v1/chat/completions` 多模态请求格式
- 🛡️ **高级反检测**：集成全面的浏览器伪装技术和人类行为模拟
- ⚡ **智能轮询**：轻量级后台任务状态监控，极低资源消耗
- 🔄 **多模态输入**：支持网络图片URL和本地文件路径自动处理
- ⚙️ **丰富参数支持**：支持生成比例、时长、AI音效等自定义参数

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
playwright install
```

### 2. 获取 Cookie
```bash
# 推荐：使用隐身模式获取最佳成功率
python tools/chatglm_cookies.py --login --stealth-mode
```

### 3. 启动服务
```bash
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 测试 API

#### 基础调用示例
```bash
curl http://127.0.0.1:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-fake-key" \
  -d '{
    "model": "glm-video",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "让这只猫在月球上跳舞，电影感，史诗级"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://img.zcool.cn/community/01a3875da97495a801219b788a5467.jpg@1280w_1l_2o_100sh.jpg"
            }
          }
        ]
      }
    ]
  }'
```

#### 带参数的高级调用示例
```bash
curl http://127.0.0.1:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-fake-key" \
  -d '{
    "model": "glm-video",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "让这只猫在月球上跳舞，电影感，史诗级"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://img.zcool.cn/community/01a3875da97495a801219b788a5467.jpg@1280w_1l_2o_100sh.jpg"
            }
          }
        ]
      }
    ],
    "aspect_ratio": "16:9",
    "duration": "10s",
    "ai_sound": true
  }'
```

## API 参数说明

### 基础参数
- `model`: 模型名称，固定为 "glm-video"
- `messages`: 消息数组，包含文本提示和图片

### 视频生成专用参数

| 参数名 | 类型 | 默认值 | 可选值 | 说明 |
|--------|------|--------|--------|------|
| `aspect_ratio` | string | null | "16:9", "9:16", "1:1", "4:3", "3:4" | 生成比例（仅文生视频模式有效） |
| `duration` | string | "5s" | "5s", "10s" | 视频生成时长 |
| `ai_sound` | boolean | false | true, false | 是否开启AI音效 |

### 参数使用说明

1. **生成比例 (aspect_ratio)**
   - 仅在文生视频模式下有效（即有图片输入时）
   - 如果不指定，将使用网站默认比例
   - 图生视频模式会忽略此参数

2. **生成时长 (duration)**
   - 默认为5秒，可选择10秒
   - 10秒时长需要VIP权限

3. **AI音效 (ai_sound)**
   - 默认关闭，可选择开启
   - 开启后会为视频添加AI生成的背景音效

### 固定参数
以下参数在所有请求中都会自动设置，无需手动指定：
- 质量更佳：自动开启
- 帧率60：自动开启
- 4K分辨率：自动开启
- 去水印：自动开启

## 项目工具

### Cookie 管理工具
- `tools/chatglm_cookies.py` - 主要的 Cookie 管理脚本，支持扫码登录和多格式导出
- `tools/gui_settings.py` - GUI 设置界面
- 支持隐身模式和高级反检测功能

### API 测试工具
- `tests/video.py` - Python 版本的 API 测试工具
- `tests/video.js` - JavaScript 版本的 API 测试工具

## Docker 部署

```bash
# 构建镜像
docker build -f docker/Dockerfile -t chatglm-api-proxy .

# 运行容器
docker run -d -p 8002:8000 \
  -v ./config/cookies.json:/app/config/cookies.json \
  --name chatglm-proxy \
  chatglm-api-proxy
```

## 📚 完整文档

详细的使用说明、故障排除和高级配置请参考：

**[📖 ChatGLM API 完整使用指南](./docs/ChatGLM_API_完整使用指南.md)**

## ⚠️ 重要提醒

- **Cookie 管理**：定期验证和更新 cookies.json 文件
- **网站更新**：CSS 选择器可能因网站更新而失效，需要定期维护
- **合规使用**：请遵守 ChatGLM 网站的服务条款和使用频率限制

## 技术架构

- **后端框架**：FastAPI + Playwright + HTTPX
- **反检测技术**：undetected-chromedriver + selenium-stealth
- **智能轮询**：任务提交后立即释放浏览器资源，后台轮询状态
- **多模态处理**：自动识别和处理网络图片URL与本地文件路径

---

**开始使用前，请务必阅读完整使用指南！** 📖