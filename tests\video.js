#!/usr/bin/env node

/**
 * ChatGLM 视频生成 API 测试工具 - JavaScript版本
 * 
 * 这是video.py的JavaScript移植版本，提供相同的功能：
 * - 支持本地图片和网络图片
 * - Base64编码
 * - API调用测试
 * - 命令行参数解析
 * 
 * 安装依赖：
 * npm install commander node-fetch
 * 
 * 使用方法：
 * node video.js --mode both --prompt "让这只猫在月球上跳舞"
 * 
 * 参数说明：
 * --mode: 测试模式 (local/remote/both)
 * --prompt: 自定义提示词
 * --url: API服务器地址
 * --verbose: 显示详细日志
 */

import fs from 'fs';
import path from 'path';
import { Command } from 'commander';
import fetch from 'node-fetch';

// --- 配置 ---
let API_URL = "http://localhost:8000/v1/chat/completions";
const LOCAL_IMAGE_PATH = "config/assets/1.jpg";
const REMOTE_IMAGE_URL = "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/de0bdceb3b61410496d0016a5b355e73~tplv-tb4s082cfz-aigc_resize:2400:2400.webp?lk3s=43402efa&x-expires=1753488000&x-signature=v5Ze0ZPA2mrIPw2ThBKnD09yhcM%3D&format=.webp";
const DEFAULT_PROMPT = "让这只猫在月球上跳舞，电影感，史诗级";
// --- 配置结束 ---

/**
 * 将本地图片编码为Base64字符串
 * @param {string} imagePath - 图片文件路径
 * @returns {string|null} Base64编码字符串或null（如果失败）
 */
function encodeImageToBase64(imagePath) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(imagePath)) {
            console.log(`❌ 文件不存在: ${imagePath}`);
            return null;
        }
        
        // 读取并编码
        const imageData = fs.readFileSync(imagePath);
        const base64Data = imageData.toString('base64');
        return base64Data;
        
    } catch (error) {
        console.log(`❌ 图片编码失败: ${error.message}`);
        return null;
    }
}

/**
 * 测试API调用
 * @param {boolean} useLocal - 是否使用本地图片
 * @param {string|null} customPrompt - 自定义提示词
 * @returns {Promise<boolean>} 测试是否成功
 */
async function testApi(useLocal = true, customPrompt = null, aspectRatio = null, duration = "5s", aiSound = false) {
    const prompt = customPrompt || DEFAULT_PROMPT;
    
    console.log(`\n${'='.repeat(60)}`);
    const testType = useLocal ? "本地图片" : "网络图片";
    console.log(`🧪 开始测试: ${testType}`);
    console.log('='.repeat(60));
    
    let imageContent;
    
    if (useLocal) {
        console.log(`🖼️ 测试本地图片: ${LOCAL_IMAGE_PATH}`);
        
        // 简单检查图片文件
        try {
            const files = fs.readdirSync('.');
            const imageFiles = files.filter(f => 
                /\.(jpg|jpeg|png|gif|webp)$/i.test(f)
            );
            console.log(`📋 找到 ${imageFiles.length} 个图片文件`);
        } catch (error) {
            console.log(`⚠️ 无法读取目录: ${error.message}`);
        }
        
        if (!fs.existsSync(LOCAL_IMAGE_PATH)) {
            console.log(`❌ 本地图片不存在: ${LOCAL_IMAGE_PATH}`);
            return false;
        }
        
        const base64Image = encodeImageToBase64(LOCAL_IMAGE_PATH);
        if (!base64Image) {
            console.log("❌ 图片编码失败，测试终止");
            return false;
        }
        
        imageContent = {
            type: "image_url",
            image_url: { url: `data:image/jpeg;base64,${base64Image}` }
        };
        
    } else {
        console.log(`🌐 测试网络图片: ${REMOTE_IMAGE_URL}`);
        imageContent = {
            type: "image_url",
            image_url: { url: REMOTE_IMAGE_URL }
        };
    }
    
    // 构建请求
    const headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-test-key"
    };
    
    const payload = {
        model: "glm-video",
        messages: [
            {
                role: "user",
                content: [
                    { type: "text", text: prompt },
                    imageContent
                ]
            }
        ]
    };

    // 添加新的视频生成参数
    if (aspectRatio) {
        payload.aspect_ratio = aspectRatio;
    }
    if (duration !== "5s") {  // 只有非默认值才添加
        payload.duration = duration;
    }
    if (aiSound) {  // 只有开启时才添加
        payload.ai_sound = aiSound;
    }
    
    console.log(`\n🚀 发送请求...`);
    console.log(`📝 提示词: ${prompt}`);
    if (aspectRatio) {
        console.log(`📐 生成比例: ${aspectRatio}`);
    }
    console.log(`⏱️ 生成时长: ${duration}`);
    console.log(`🔊 AI音效: ${aiSound ? '开启' : '关闭'}`);
    
    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload),
            timeout: 600000 // 600秒超时
        });
        
        console.log(`📊 状态码: ${response.status}`);
        
        // 检查HTTP状态码
        if (!response.ok) {
            console.log(`❌ API返回错误状态码: ${response.status}`);
            try {
                const errorJson = await response.json();
                console.log("📄 错误详情:");
                console.log(JSON.stringify(errorJson, null, 2));
            } catch (jsonError) {
                console.log("📄 原始错误响应:");
                const errorText = await response.text();
                console.log(errorText);
            }
            return false;
        }
        
        try {
            const responseJson = await response.json();
            console.log("✅ 收到响应");
            
            // 检查并打印视频链接
            try {
                const content = responseJson.choices[0].message.content;
                // 从Markdown链接中提取URL: [text](url)
                const match = content.match(/\((https:\/\/.*?)\)/);
                if (match) {
                    const videoUrl = match[1];
                    console.log(`🎬 视频链接: ${videoUrl}`);
                    return true;
                } else {
                    console.log("⚠️ 响应中未找到有效的视频链接。");
                    console.log(`   > 响应内容: ${content}`);
                    return false;
                }
            } catch (extractError) {
                console.log("⚠️ 响应格式异常，无法提取视频信息。");
                console.log(`错误详情: ${extractError.message}`);
                return false;
            }
                
        } catch (jsonError) {
            console.log("❌ 响应不是有效的JSON格式");
            console.log("📄 原始响应:");
            const responseText = await response.text();
            console.log(responseText);
            return false;
        }
            
    } catch (error) {
        if (error.name === 'AbortError') {
            console.log("❌ 请求超时（600秒）");
        } else if (error.code === 'ECONNREFUSED') {
            console.log("❌ 连接失败，请确保API服务器正在运行");
        } else {
            console.log(`❌ 请求失败: ${error.message}`);
            console.log("详细错误信息:");
            console.error(error);
        }
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    const program = new Command();
    
    program
        .name('video-test')
        .description('ChatGLM视频生成API测试工具')
        .option('-m, --mode <mode>', '测试模式: local(本地图片), remote(网络图片), both(两种都测试)', 'both')
        .option('-p, --prompt <prompt>', '自定义提示词')
        .option('-u, --url <url>', 'API服务器地址', API_URL)
        .option('-v, --verbose', '显示详细日志')
        // 新增视频生成参数
        .option('--aspect-ratio <ratio>', '生成比例 (仅文生视频模式): 16:9, 9:16, 1:1, 4:3, 3:4')
        .option('--duration <duration>', '生成时长: 5s 或 10s', '5s')
        .option('--ai-sound', '开启AI音效')
        .parse();
    
    const options = program.opts();
    API_URL = options.url;
    
    console.log("=".repeat(60));
    console.log("ChatGLM 视频生成 API 测试工具 - JavaScript版本");
    console.log("=".repeat(60));
    console.log(`🌐 API地址: ${API_URL}`);
    console.log(`📝 提示词: ${options.prompt || DEFAULT_PROMPT}`);
    console.log(`📐 生成比例: ${options.aspectRatio || '默认'}`);
    console.log(`⏱️ 生成时长: ${options.duration}`);
    console.log(`🔊 AI音效: ${options.aiSound ? '开启' : '关闭'}`);
    console.log(`🕐 开始时间: ${new Date().toLocaleString('zh-CN')}`);
    
    let successCount = 0;
    let totalTests = 0;
    
    if (options.mode === 'local' || options.mode === 'both') {
        if (await testApi(true, options.prompt, options.aspectRatio, options.duration, options.aiSound)) {
            successCount++;
        }
        totalTests++;
    }

    if (options.mode === 'remote' || options.mode === 'both') {
        if (await testApi(false, options.prompt, options.aspectRatio, options.duration, options.aiSound)) {
            successCount++;
        }
        totalTests++;
    }
    
    console.log("\n" + "=".repeat(60));
    console.log(`🏁 测试完成: ${successCount}/${totalTests} 成功`);
    if (successCount === totalTests) {
        console.log("🎉 所有测试都通过了！");
    } else if (successCount > 0) {
        console.log("⚠️ 部分测试失败，请检查上面的错误信息");
    } else {
        console.log("❌ 所有测试都失败了，请检查服务器状态和配置");
    }
    console.log("=".repeat(60));
}

// 执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error('程序执行出错:', error);
        process.exit(1);
    });
} 