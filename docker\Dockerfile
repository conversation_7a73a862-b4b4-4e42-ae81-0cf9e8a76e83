# 步骤 1: 选择一个官方的、精简的 Python 基础镜像
# 使用一个具体的版本号可以保证环境的稳定性
FROM python:3.12-slim

# 步骤 2: 设置工作目录
# 容器内所有后续操作都将在此目录下进行
WORKDIR /app

# 步骤 3: 优化依赖安装
# 先只复制 requirements.txt 文件。这样，如果这个文件没有变化，Docker会使用缓存，
# 无需重新安装所有依赖，极大地加快了后续的构建速度。
COPY requirements.txt .

# 安装 Python 依赖
# --no-cache-dir 选项可以减小最终镜像的体积
RUN pip install --no-cache-dir -r requirements.txt

# 步骤 4: 安装 Playwright 及其浏览器依赖
# 这是非常关键的一步！
# --with-deps 参数会自动使用apt-get等工具安装Playwright运行所需的所有系统库，
# 避免了我们手动处理复杂的系统依赖问题。
# 我们只安装 chromium，因为脚本中只用到了它，这也能减小镜像体积。
RUN playwright install --with-deps chromium

# 步骤 5: 复制项目代码
# 将当前目录下的所有文件（除了.dockerignore中指定的）复制到容器的 /app 目录下
COPY . .

# 步骤 6: 声明服务端口
# 告诉 Docker 容器内的应用程序将监听 8000 端口
EXPOSE 8000

# 步骤 7: 设置容器启动命令
# 当容器启动时，执行 uvicorn 命令来运行 FastAPI 应用
# 使用 0.0.0.0 作为主机地址，使其可以从外部访问
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]