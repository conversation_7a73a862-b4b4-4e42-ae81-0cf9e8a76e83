#!/usr/bin/env node

/**
 * ChatGLM 视频生成 API HTTP 请求示例 - JavaScript版本
 * 
 * 这个文件展示了如何使用纯HTTP请求调用ChatGLM视频生成API，
 * 对应README.md中的curl命令示例。
 * 
 * 功能特性：
 * - 基础调用示例（文本+图片）
 * - 高级调用示例（包含aspect_ratio、duration、ai_sound参数）
 * - 详细的请求和响应处理
 * - 错误处理和状态检查
 * 
 * 使用方法：
 * node tests/http_test.js
 * 
 * 依赖：
 * npm install node-fetch
 */

import fetch from 'node-fetch';

// --- 配置 ---
const API_URL = "http://127.0.0.1:8000/v1/chat/completions";
// 示例图片URL（来自README.md）
const EXAMPLE_IMAGE_URL = "https://img.zcool.cn/community/01a3875da97495a801219b788a5467.jpg@1280w_1l_2o_100sh.jpg";
// 示例提示词
const EXAMPLE_PROMPT = "让这只猫在月球上跳舞，电影感，史诗级";
// --- 配置结束 ---

/**
 * 基础HTTP请求示例
 * 
 * 对应README.md中的基础调用示例：
 * - 包含文本提示和图片URL
 * - 使用默认参数
 */
async function basicHttpRequest() {
    console.log("\n" + "=".repeat(60));
    console.log("🧪 基础HTTP请求示例");
    console.log("=".repeat(60));
    
    // 请求头
    const headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-your-fake-key"
    };
    
    // 请求体（对应README中的基础示例）
    const payload = {
        model: "glm-video",
        messages: [
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: EXAMPLE_PROMPT
                    },
                    {
                        type: "image_url",
                        image_url: {
                            url: EXAMPLE_IMAGE_URL
                        }
                    }
                ]
            }
        ]
    };
    
    console.log(`📝 请求URL: ${API_URL}`);
    console.log(`📝 提示词: ${EXAMPLE_PROMPT}`);
    console.log(`🖼️ 图片URL: ${EXAMPLE_IMAGE_URL}`);
    console.log(`⚙️ 参数: 使用默认参数`);
    
    try {
        console.log(`\n🚀 发送HTTP请求...`);
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload),
            timeout: 600000 // 600秒超时
        });
        
        console.log(`📊 HTTP状态码: ${response.status}`);
        
        if (response.ok) {
            const responseData = await response.json();
            console.log("✅ 请求成功");
            
            // 提取视频链接
            try {
                const content = responseData.choices[0].message.content;
                console.log(`📄 响应内容: ${content}`);
                
                // 从Markdown链接中提取URL
                const match = content.match(/\((https:\/\/.*?)\)/);
                if (match) {
                    const videoUrl = match[1];
                    console.log(`🎬 生成的视频链接: ${videoUrl}`);
                } else {
                    console.log("⚠️ 未找到视频链接");
                }
                
            } catch (error) {
                console.log(`❌ 响应格式错误: ${error.message}`);
            }
            
        } else {
            console.log(`❌ 请求失败，状态码: ${response.status}`);
            try {
                const errorData = await response.json();
                console.log(`📄 错误详情: ${JSON.stringify(errorData, null, 2)}`);
            } catch {
                const errorText = await response.text();
                console.log(`📄 原始响应: ${errorText}`);
            }
        }
        
    } catch (error) {
        if (error.name === 'AbortError') {
            console.log("❌ 请求超时（600秒）");
        } else if (error.code === 'ECONNREFUSED') {
            console.log("❌ 连接失败，请确保API服务器正在运行");
        } else {
            console.log(`❌ 请求异常: ${error.message}`);
        }
    }
}

/**
 * 高级HTTP请求示例
 * 
 * 对应README.md中的带参数的高级调用示例：
 * - 包含文本提示和图片URL
 * - 包含aspect_ratio、duration、ai_sound等参数
 */
async function advancedHttpRequest() {
    console.log("\n" + "=".repeat(60));
    console.log("🧪 高级HTTP请求示例（带参数）");
    console.log("=".repeat(60));
    
    // 请求头
    const headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-your-fake-key"
    };
    
    // 请求体（对应README中的高级示例）
    const payload = {
        model: "glm-video",
        messages: [
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: EXAMPLE_PROMPT
                    },
                    {
                        type: "image_url",
                        image_url: {
                            url: EXAMPLE_IMAGE_URL
                        }
                    }
                ]
            }
        ],
        // 视频生成专用参数
        aspect_ratio: "16:9",  // 生成比例
        duration: "10s",       // 生成时长
        ai_sound: true         // AI音效
    };
    
    console.log(`📝 请求URL: ${API_URL}`);
    console.log(`📝 提示词: ${EXAMPLE_PROMPT}`);
    console.log(`🖼️ 图片URL: ${EXAMPLE_IMAGE_URL}`);
    console.log(`📐 生成比例: ${payload.aspect_ratio}`);
    console.log(`⏱️ 生成时长: ${payload.duration}`);
    console.log(`🔊 AI音效: ${payload.ai_sound ? '开启' : '关闭'}`);
    
    try {
        console.log(`\n🚀 发送HTTP请求...`);
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload),
            timeout: 600000 // 600秒超时
        });
        
        console.log(`📊 HTTP状态码: ${response.status}`);
        
        if (response.ok) {
            const responseData = await response.json();
            console.log("✅ 请求成功");
            
            // 提取视频链接
            try {
                const content = responseData.choices[0].message.content;
                console.log(`📄 响应内容: ${content}`);
                
                // 从Markdown链接中提取URL
                const match = content.match(/\((https:\/\/.*?)\)/);
                if (match) {
                    const videoUrl = match[1];
                    console.log(`🎬 生成的视频链接: ${videoUrl}`);
                } else {
                    console.log("⚠️ 未找到视频链接");
                }
                
            } catch (error) {
                console.log(`❌ 响应格式错误: ${error.message}`);
            }
            
        } else {
            console.log(`❌ 请求失败，状态码: ${response.status}`);
            try {
                const errorData = await response.json();
                console.log(`📄 错误详情: ${JSON.stringify(errorData, null, 2)}`);
            } catch {
                const errorText = await response.text();
                console.log(`📄 原始响应: ${errorText}`);
            }
        }
        
    } catch (error) {
        if (error.name === 'AbortError') {
            console.log("❌ 请求超时（600秒）");
        } else if (error.code === 'ECONNREFUSED') {
            console.log("❌ 连接失败，请确保API服务器正在运行");
        } else {
            console.log(`❌ 请求异常: ${error.message}`);
        }
    }
}

/**
 * 主函数：演示基础和高级HTTP请求
 */
async function main() {
    console.log("=".repeat(60));
    console.log("ChatGLM 视频生成 API HTTP 请求示例 - JavaScript版本");
    console.log("=".repeat(60));
    console.log(`🕐 开始时间: ${new Date().toLocaleString('zh-CN')}`);
    console.log(`🌐 API地址: ${API_URL}`);
    console.log("\n📖 说明：");
    console.log("   这个脚本展示了如何使用JavaScript的fetch API");
    console.log("   发送HTTP请求到ChatGLM视频生成API");
    console.log("   对应README.md中的curl命令示例");
    
    // 执行基础请求示例
    await basicHttpRequest();
    
    // 等待一段时间再执行高级请求
    console.log(`\n⏳ 等待5秒后执行高级请求示例...`);
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 执行高级请求示例
    await advancedHttpRequest();
    
    console.log("\n" + "=".repeat(60));
    console.log("🏁 HTTP请求示例演示完成");
    console.log("=".repeat(60));
    console.log("\n💡 提示：");
    console.log("   - 确保API服务器正在运行（uvicorn src.main:app --port 8000）");
    console.log("   - 确保cookies.json文件已正确配置");
    console.log("   - 视频生成可能需要较长时间，请耐心等待");
}

// 执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error('程序执行出错:', error);
        process.exit(1);
    });
}
