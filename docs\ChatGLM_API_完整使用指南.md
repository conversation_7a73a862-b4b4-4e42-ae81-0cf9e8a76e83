# ChatGLM API 完整使用指南

## 📖 项目介绍

本项目是一个中间层代理服务器，将智谱清言（chatglm.cn）网站的视频生成功能封装成与 OpenAI API 完全兼容的接口。支持任何兼容 OpenAI Vision API 的客户端无缝对接。

### 核心特性
- 🎯 **OpenAI Vision API 兼容**：完全模拟 `/v1/chat/completions` 多模态请求格式
- 🛡️ **高级反检测**：集成全面的浏览器伪装技术和人类行为模拟
- ⚡ **智能轮询**：轻量级后台任务状态监控，极低资源消耗
- 🔄 **多模态输入**：支持网络图片URL和本地文件路径自动处理

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Chrome 浏览器
- 稳定的网络连接

### 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **安装浏览器驱动**
```bash
playwright install
```

3. **获取 Cookie（重要）**
```bash
# 推荐：使用隐身模式获取最佳成功率
python tools/chatglm_cookies.py --login --stealth-mode

# 标准模式
python tools/chatglm_cookies.py --login
```

4. **启动服务**
```bash
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

### 快速测试
```bash
curl http://127.0.0.1:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-fake-key" \
  -d '{
    "model": "glm-video",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "让这只猫在月球上跳舞，电影感，史诗级"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://img.zcool.cn/community/01a3875da97495a801219b788a5467.jpg@1280w_1l_2o_100sh.jpg"
            }
          }
        ]
      }
    ]
  }'
```

## 🍪 Cookie 管理详解

### 基本使用

**扫码登录**
```bash
# 隐身模式（推荐，最高成功率）
python tools/chatglm_cookies.py --login --stealth-mode

# 标准模式
python tools/chatglm_cookies.py --login

# 增加重试次数
python tools/chatglm_cookies.py --login --stealth-mode --retries 5
```

**验证 Cookie**
```bash
python tools/chatglm_cookies.py --validate --file config/cookies.json
```

**导出不同格式**
```bash
# JSON格式（默认）
python tools/chatglm_cookies.py --export --format json

# Python字典格式
python tools/chatglm_cookies.py --export --format py --output cookies.py

# Netscape格式
python tools/chatglm_cookies.py --export --format txt --output cookies.txt
```

### 登录流程
1. 脚本自动打开Chrome浏览器
2. 跳转到ChatGLM登录页面
3. 使用手机ChatGLM APP扫描二维码
4. 在手机上确认登录
5. 回到终端按回车键完成Cookie保存

## 🔧 滑动验证问题解决

### 问题症状
浏览器可以正常滑动，但网站无法验证通过。

### 解决方案

**1. 使用增强反检测模式**
```bash
# 最高成功率配置
python tools/chatglm_cookies.py --login --stealth-mode --retries 5
```

**2. 安装完整反检测依赖**
```bash
pip install undetected-chromedriver selenium-stealth fake-useragent setuptools
```

**3. 技术原理**
- 贝塞尔曲线轨迹生成：模拟真实手部移动
- 指纹随机化：Canvas、WebGL、屏幕分辨率随机化
- 行为模拟：随机鼠标移动、阅读停顿、微颤抖动
- 环境伪装：User-Agent、语言、时区随机化

### 成功率提升建议
1. 使用 `--stealth-mode` 参数
2. 避开网站高峰期使用
3. 保持稳定网络环境
4. 每次尝试间隔1-2分钟
5. 定期清理浏览器缓存

## 🛠️ 故障排除指南

### Chrome 相关问题

**错误：ChromeDriver版本不匹配**
```bash
# 更新依赖
pip install --upgrade selenium undetected-chromedriver

# 检查Chrome版本
# 打开Chrome -> 设置 -> 关于Chrome
```

**错误：权限不足**
```bash
# Windows：以管理员身份运行PowerShell
# 关闭所有Chrome进程
# 检查防火墙设置
```

### Cookie 相关问题

**Cookie验证失败**
```bash
# 重新获取Cookie
python tools/chatglm_cookies.py --login --stealth-mode

# 验证网络连接
curl -I https://chatglm.cn
```

**登录检测失败**
- 确保手机APP成功确认登录
- 检查网络连接稳定性
- 使用最新版本ChatGLM APP
- 重新扫码登录

### 系统诊断

**环境检查**
```bash
# Python版本（需要3.8+）
python --version

# 依赖检查
pip list | findstr selenium
pip list | findstr undetected

# Chrome版本检查
# 访问 chrome://version/
```

**渐进式测试**
```bash
# 测试1：基础模式
python tools/chatglm_cookies.py --login

# 测试2：隐身模式
python tools/chatglm_cookies.py --login --stealth-mode

# 测试3：高重试次数
python tools/chatglm_cookies.py --login --stealth-mode --retries 10
```

## 📡 API 测试工具

### Python 测试工具
```bash
# 测试本地图片
python tests/video.py --mode local

# 测试网络图片
python tests/video.py --mode remote

# 自定义提示词
python tests/video.py --prompt "创建一个机器人跳舞的视频"
```

### JavaScript 测试工具
```bash
# 安装依赖
npm install commander node-fetch

# 运行测试（在tests目录下）
cd tests
node video.js --mode both --verbose
```

## 🐳 Docker 部署

### 构建镜像
```bash
docker build -f docker/Dockerfile -t chatglm-api-proxy .
```

### 运行容器
```bash
docker run -d -p 8002:8000 \
  -v ./config/cookies.json:/app/config/cookies.json \
  --name chatglm-proxy \
  chatglm-api-proxy
```

### 容器管理
```bash
# 查看日志
docker logs -f chatglm-proxy

# 停止服务
docker stop chatglm-proxy

# 重启服务
docker restart chatglm-proxy
```

## ⚠️ 重要注意事项

1. **Cookie安全**：不要将cookies.json上传到公共仓库
2. **使用频率**：避免过于频繁的请求，防止IP被封
3. **网站更新**：CSS选择器可能因网站更新而失效，需要定期维护
4. **合规使用**：请遵守ChatGLM网站的服务条款

## 🔗 相关文件说明

### 核心源代码 (src/)
- `src/main.py` - API服务器主程序
- `src/video_generator.py` - 视频生成核心逻辑
- `src/config_manager.py` - 配置管理模块

### 工具脚本 (tools/)
- `tools/chatglm_cookies.py` - Cookie管理工具
- `tools/gui_settings.py` - GUI设置界面

### 测试工具 (tests/)
- `tests/video.py` - Python API测试工具
- `tests/video.js` - JavaScript API测试工具

### 配置文件 (config/)
- `config/config.json` - 应用配置文件
- `config/cookies.json` - Cookie数据文件
- `config/assets/1.jpg` - 测试图片

### 部署文件
- `docker/Dockerfile` - Docker构建文件
- `requirements.txt` - Python依赖列表

## 📞 技术支持

如遇问题，请提供：
- 操作系统版本
- Python和Chrome版本
- 完整错误日志
- 已尝试的解决方案

---

**祝您使用愉快！** 🎉
