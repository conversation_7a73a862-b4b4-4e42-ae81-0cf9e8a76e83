import asyncio
import json
import os
import tempfile
import time
import re
import base64
from typing import Dict, Any

import aiofiles
import httpx
from playwright.async_api import async_playwright

# CSS选择器配置 - 需要根据实际网页结构更新
PROMPT_TEXTAREA_SELECTOR = "textarea[placeholder*='输入']"
GENERATE_BUTTON_SELECTOR = "button:has-text('生成')"
VIDEO_RESULT_SELECTOR = "video"

# 弹窗相关选择器
POPUP_SELECTORS = {
    "new_feature_popup": ".new-featrue-container",
    "close_button": ".close-btn-container .close-btn, .close-btn-container svg"
}

# Cookie sameSite值标准化映射
SAMESITE_MAPPING = {
    "unspecified": "Lax",
    "no_restriction": "None", 
    "strict": "Strict",
    "lax": "Lax",
    "none": "None"
}

# --- 1. Cookie处理函数 ---
def normalize_cookies(cookies: list) -> list:
    """
    标准化Cookie格式，确保sameSite值符合Playwright要求
    """
    normalized_cookies = []
    for cookie in cookies:
        normalized_cookie = cookie.copy()
        
        # 标准化sameSite值
        same_site = cookie.get('sameSite', 'Lax')
        if isinstance(same_site, str):
            same_site_lower = same_site.lower()
            normalized_cookie['sameSite'] = SAMESITE_MAPPING.get(same_site_lower, 'Lax')
        else:
            normalized_cookie['sameSite'] = 'Lax'
        
        # 确保必需字段存在
        normalized_cookie.setdefault('domain', '.chatglm.cn')
        normalized_cookie.setdefault('path', '/')
        normalized_cookie.setdefault('httpOnly', False)
        normalized_cookie.setdefault('secure', False)
        
        # 处理过期时间字段兼容性
        if 'expirationDate' in normalized_cookie and 'expires' not in normalized_cookie:
            normalized_cookie['expires'] = normalized_cookie['expirationDate']
        
        normalized_cookies.append(normalized_cookie)
    
    return normalized_cookies

# --- 2. 核心逻辑：处理图片输入 ---
async def handle_image_input(image_source: str) -> str:
    """
    智能处理图片输入。它能处理URL、本地文件路径和Base64数据URI。
    - URL: 下载到临时文件。
    - Base64: 解码并存入临时文件。
    - 本地路径: 直接使用。
    """
    if image_source.lower().startswith('data:image'):
        print("💡 输入为Base64数据URI，正在解码...")
        try:
            # 找到 "base64," 后的数据部分
            header, encoded = image_source.split(',', 1)
            image_data = base64.b64decode(encoded)
            
            # 从header中提取文件后缀，如 "image/jpeg" -> ".jpg"
            file_extension = header.split(';')[0].split('/')[1]
            suffix = f".{file_extension}" if file_extension else ".jpg"

            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                temp_file_path = tmp_file.name
            
            async with aiofiles.open(temp_file_path, 'wb') as f:
                await f.write(image_data)
            
            print(f"✅ Base64图片已临时保存到: {temp_file_path}")
            return temp_file_path
        except Exception as e:
            raise ValueError(f"解析或解码Base64数据URI失败: {e}")

    is_url = image_source.lower().startswith(('http://', 'https://'))
    
    if is_url:
        print(f"🔗 输入为URL，正在下载: {image_source}")
        with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as tmp_file:
            temp_file_path = tmp_file.name
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_source, follow_redirects=True, timeout=60)
                response.raise_for_status()
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    await f.write(response.content)
                print(f"✅ 图片已临时下载到: {temp_file_path}")
                return temp_file_path
            except Exception as e:
                os.remove(temp_file_path)
                raise IOError(f"下载图片URL失败: {e}")
    else:
        if not os.path.exists(image_source):
            raise FileNotFoundError(f"本地图片文件不存在: {image_source}")
        print(f"📁 输入为本地文件路径: {image_source}")
        return image_source

# --- 2. 弹窗处理函数 ---

async def handle_popup_dialogs(page):
    """
    处理页面上可能出现的各种弹窗，如更新公告等。

    Args:
        page: Playwright页面对象
    """
    try:
        print("🔍 检查页面弹窗...")

        # 等待页面稳定
        await asyncio.sleep(2)

        # 检查更新公告弹窗
        popup_selector = POPUP_SELECTORS["new_feature_popup"]
        popup_elements = await page.locator(popup_selector).count()

        if popup_elements > 0:
            print("📢 检测到更新公告弹窗，正在关闭...")

            # 查找并点击关闭按钮
            close_button_selector = POPUP_SELECTORS["close_button"]
            close_button = page.locator(close_button_selector).first

            # 等待关闭按钮可见并点击
            await close_button.wait_for(state="visible", timeout=5000)
            await close_button.click()

            # 等待弹窗消失
            await page.locator(popup_selector).wait_for(state="hidden", timeout=5000)
            print("✅ 更新公告弹窗已关闭")
        else:
            print("ℹ️ 未检测到弹窗")

    except Exception as e:
        print(f"⚠️ 处理弹窗时出现问题（可忽略）: {e}")
        # 弹窗处理失败不应影响主流程，所以只记录日志

# --- 3. 自动化流程函数 ---

async def initialize_browser_and_page(cookies_path: str = None, config=None):
    """初始化浏览器、上下文和页面，应用所有反检测措施。"""
    # 如果没有传入配置，使用默认值
    if config is None:
        try:
            from .config_manager import get_config
            config = get_config()
        except ImportError:
            try:
                # 如果相对导入失败，尝试绝对导入
                from src.config_manager import get_config
                config = get_config()
            except ImportError:
                # 如果都失败了，使用简单的默认配置
                class SimpleConfig:
                    def __init__(self):
                        self.browser = type('obj', (object,), {
                            'headless': False,
                            'window_width': 1920,
                            'window_height': 1080,
                            'user_agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                        })()
                config = SimpleConfig()
    
    playwright = await async_playwright().start()
    browser_args = ['--no-sandbox', '--disable-setuid-sandbox', '--disable-blink-features=AutomationControlled']

    try:
        # 尝试启动浏览器
        browser = await playwright.chromium.launch(headless=config.browser.headless, args=browser_args)
    except Exception as e:
        print(f"❌ 浏览器启动失败: {e}")
        print("🔧 尝试解决方案:")
        print("1. 运行: playwright install")
        print("2. 或运行: playwright install chromium")
        print("3. 或设置 headless=true 在配置文件中")

        # 尝试强制使用无头模式
        print("🔄 尝试使用无头模式...")
        try:
            browser = await playwright.chromium.launch(headless=True, args=browser_args)
            print("✅ 无头模式启动成功")
        except Exception as e2:
            print(f"❌ 无头模式也失败: {e2}")
            raise Exception("无法启动浏览器。请运行 'playwright install' 安装浏览器。")
    context = await browser.new_context(
        user_agent=config.browser.user_agent,
        viewport={'width': config.browser.window_width, 'height': config.browser.window_height}
    )
    if cookies_path and os.path.exists(cookies_path):
        with open(cookies_path, 'r') as f:
            raw_cookies = json.load(f)
        # 标准化Cookie格式
        cookies = normalize_cookies(raw_cookies)
        print(f"🍪 标准化了 {len(cookies)} 个Cookie")
        await context.add_cookies(cookies)
    page = await context.new_page()
    await page.add_init_script(script="Object.defineProperty(navigator, 'webdriver', { get: () => undefined });")
    await page.goto('https://chatglm.cn/video?lang=zh', wait_until='networkidle', timeout=60000)

    # 处理可能出现的弹窗
    await handle_popup_dialogs(page)

    print("🚀 浏览器初始化并导航成功。")
    return playwright, browser, page

async def upload_image_and_crop(page, image_path: str):
    """上传图片并处理裁切。"""
    print(f"🖼️ 正在上传图片: {image_path}")
    file_input = page.locator('input[type="file"]').first
    await file_input.set_input_files(image_path)
    await asyncio.sleep(2)
    try:
        await page.locator('button:has-text("上传")').first.click(timeout=5000)
    except Exception:
        print("未找到或无需点击'上传'按钮，继续流程。")
    await page.wait_for_selector('textarea[placeholder*="视频"]', timeout=30000)
    print("✅ 图片上传成功。")

async def set_parameters_and_options(page, prompt_text: str, aspect_ratio: str = None, duration: str = "5s", ai_sound: bool = False, is_text_to_video: bool = True):
    """设置提示词和高级参数，支持动态参数配置。

    Args:
        page: Playwright页面对象
        prompt_text: 提示词文本
        aspect_ratio: 生成比例 (仅文生视频模式) - "16:9", "9:16", "1:1", "4:3", "3:4"
        duration: 生成时长 - "5s" 或 "10s"
        ai_sound: 是否开启AI音效 - True/False
        is_text_to_video: 是否为文生视频模式 - True为文生视频，False为图生视频
    """
    print("⚙️ 正在设置参数...")
    if prompt_text and prompt_text.strip():
        await page.locator('textarea[placeholder*="视频"]').first.fill(prompt_text)

    # 设置固定的基础参数（质量更佳、4K、帧率60）
    await page.locator('div.prompt-item:has-text("基础参数")').first.click()
    await asyncio.sleep(0.5)
    await page.locator('div.desc:has-text("质量更佳")').first.click()
    await asyncio.sleep(0.5)
    await page.locator('div.desc:has-text("4K")').first.click()
    await asyncio.sleep(0.5)
    await page.locator('div.desc:has-text("帧率60")').first.click()
    await asyncio.sleep(0.5)

    # 设置生成比例（仅文生视频模式）
    if is_text_to_video and aspect_ratio:
        print(f"📐 设置生成比例: {aspect_ratio}")
        try:
            # 查找并点击对应的比例选项
            ratio_selector = f'div.option-item.ratio-item:has(span:text("{aspect_ratio}"))'
            await page.locator(ratio_selector).first.click()
            await asyncio.sleep(0.5)
            print(f"✅ 已设置生成比例为: {aspect_ratio}")
        except Exception as e:
            print(f"⚠️ 设置生成比例失败: {e}，将使用默认比例")

    # 设置生成时长
    if duration == "10s":
        print("⏱️ 设置生成时长为10秒")
        try:
            # 先点击5s按钮打开菜单
            await page.locator('div.prompt-item:has-text("5s")').first.click()
            await asyncio.sleep(0.5)
            # 然后点击10s选项
            await page.locator('div.label:has(span:text("10s"))').first.click()
            await asyncio.sleep(0.5)
            print("✅ 已设置生成时长为10秒")
        except Exception as e:
            print(f"⚠️ 设置生成时长失败: {e}，将使用默认5秒")
    else:
        print("⏱️ 使用默认生成时长5秒")

    # 设置AI音效
    if ai_sound:
        print("🔊 开启AI音效")
        try:
            # 点击AI音效(关)按钮打开菜单
            await page.locator('div.prompt-item:has-text("AI音效(关)")').first.click()
            await asyncio.sleep(0.5)
            # 点击开启选项
            await page.locator('div.title-wrap:has(span:text("开启"))').first.click()
            await asyncio.sleep(0.5)
            print("✅ 已开启AI音效")
        except Exception as e:
            print(f"⚠️ 开启AI音效失败: {e}，将保持默认关闭状态")
    else:
        print("🔇 AI音效保持关闭状态")

    print("✅ 参数设置完成。")

async def start_generation_and_poll(page, browser, cookies_path: str) -> Dict[str, Any]:
    """
    点击生成，捕获API信息，关闭浏览器，然后携带完整Cookie在后台轮询。
    """
    future_api_info = asyncio.Future()

    async def handle_response(response):
        if "/v1/chat" in response.url and response.request.method == "POST" and not future_api_info.done():
            try:
                data = await response.json()
                if data.get("result", {}).get("chat_id"):
                    chat_id = data.get("result").get("chat_id")
                    headers = response.request.headers
                    future_api_info.set_result({"chat_id": chat_id, "headers": headers})
                    print(f"✅ 成功从POST响应中捕获 Chat ID: {chat_id}")
            except Exception:
                pass

    page.on("response", handle_response)
    
    generate_button_selector = 'div[data-v-2c1e1468].btn-group'
    await page.locator(generate_button_selector).first.click()
    print("🖱️ 已点击生成，等待捕获API信息...")

    try:
        api_info = await asyncio.wait_for(future_api_info, timeout=20)
    except asyncio.TimeoutError:
        raise Exception("捕获初始API信息超时，请检查生成按钮选择器或网络。")
    finally:
        page.remove_listener("response", handle_response)

    print("✅ 成功捕获API信息，关闭浏览器以节省资源...")
    await browser.close()
    
    # 加载并使用完整的Cookie
    cookies = None
    if cookies_path and os.path.exists(cookies_path):
        with open(cookies_path, 'r') as f:
            cookie_list = json.load(f)
            cookies = {cookie['name']: cookie['value'] for cookie in cookie_list}
            print(f"🍪 已加载 {len(cookies)} 个Cookie用于后台轮询。")

    polling_url = f"https://chatglm.cn/chatglm/video-api/v1/chat/status/{api_info['chat_id']}"
    headers = {
        'Authorization': api_info['headers'].get('authorization', ''),
        'X-Sign': api_info['headers'].get('x-sign', ''),
        'User-Agent': api_info['headers'].get('user-agent', ''),
        'Referer': 'https://chatglm.cn/video',
    }

    print("📡 开始后台轮询视频状态 (携带完整Cookie)...")
    async with httpx.AsyncClient(headers=headers, cookies=cookies, timeout=30) as client:
        for _ in range(120):
            response = await client.get(polling_url)
            try:
                response.raise_for_status()
                data = response.json()
                result = data.get("result", {})
                status = result.get("status")
                print(f"🔄 轮询状态: {status}, 进度: {result.get('plan', 'N/A')}")
                if status == 'finished' and result.get("video_url"):
                    print("🎉 视频生成完成！")
                    return {"success": True, "video_url": result["video_url"]}
                elif status in ['failed', 'error']:
                    raise Exception(f"视频生成失败: {result.get('msg', '未知错误')}")
            except (json.JSONDecodeError, httpx.HTTPStatusError) as e:
                print(f"❌ 轮询失败: {e}")
                print("--- 服务器响应内容 ---")
                print(response.text)
                print("---------------------")
                raise Exception("轮询时认证失败或服务器返回无效数据。请检查Cookie是否过期。")
            
            await asyncio.sleep(5)
            
    raise TimeoutError("轮询超时，视频未在10分钟内生成。")

# --- 3. 主流程编排函数 ---
async def run_video_generation_flow(image_source: str, prompt: str, cookies_path: str,
                                   aspect_ratio: str = None, duration: str = "5s",
                                   ai_sound: bool = False) -> Dict[str, Any]:
    """
    编排整个视频生成流程，并确保临时文件的清理。

    Args:
        image_source: 图片源（URL、本地路径或Base64）
        prompt: 提示词
        cookies_path: Cookie文件路径
        aspect_ratio: 生成比例（仅文生视频）- "16:9", "9:16", "1:1", "4:3", "3:4"
        duration: 生成时长 - "5s" 或 "10s"
        ai_sound: 是否开启AI音效
    """
    temp_image_path = None
    playwright = None
    browser = None
    try:
        # 步骤1: 处理输入图片
        path_to_upload = await handle_image_input(image_source)
        if path_to_upload != image_source:
            temp_image_path = path_to_upload
            
        # 步骤2: 启动浏览器
        try:
            from .config_manager import get_config
            config = get_config()
        except ImportError:
            try:
                # 如果相对导入失败，尝试绝对导入
                from src.config_manager import get_config
                config = get_config()
            except ImportError:
                # 如果都失败了，使用简单的默认配置
                class SimpleConfig:
                    def __init__(self):
                        self.browser = type('obj', (object,), {
                            'headless': False,
                            'window_width': 1920,
                            'window_height': 1080,
                            'user_agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                        })()
                config = SimpleConfig()
        playwright, browser, page = await initialize_browser_and_page(cookies_path, config)
        
        # 步骤3: 上传图片
        await upload_image_and_crop(page, path_to_upload)

        # 判断是文生视频还是图生视频模式
        # 如果有图片输入，则为图生视频模式；否则为文生视频模式
        is_text_to_video = not (image_source and image_source.strip())

        # 步骤4: 设置参数
        await set_parameters_and_options(page, prompt, aspect_ratio, duration, ai_sound, is_text_to_video)
        
        # 步骤5: 开始生成并轮询
        result = await start_generation_and_poll(page, browser, cookies_path)
        browser = None
        
        return result

    except Exception as e:
        print(f"❌ 流程执行出错: {e}")
        return {"success": False, "error": str(e)}
    finally:
        # 步骤6: 清理资源
        if browser:
            await browser.close()
            print("🔴 异常退出，浏览器已关闭。")
        if playwright:
            await playwright.stop()
        if temp_image_path and os.path.exists(temp_image_path):
            os.remove(temp_image_path)
            print(f"🗑️ 临时图片文件已清理: {temp_image_path}")

# --- 本地测试入口 ---
if __name__ == "__main__":
    async def main():
        image_input_local = "1.jpg"  # 使用相对路径
        prompt_input = "一只戴着墨镜的猫在沙滩上弹吉他，电影质感，4k，60帧"
        # 查找cookies.json文件
        possible_paths = [
            "cookies.json",
            "../cookies.json",
            "config/cookies.json",
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "cookies.json")
        ]

        cookies_file = None
        for path in possible_paths:
            if os.path.exists(path):
                cookies_file = path
                break

        print("--- 开始本地测试 ---")
        if not cookies_file:
            print(f"警告: cookies.json 文件不存在，将以未登录状态运行，很可能失败。")
            print(f"已检查路径: {', '.join(possible_paths)}")
            cookies_file = "cookies.json"  # 使用默认路径作为备选
        else:
            print(f"找到Cookie文件: {cookies_file}")

        result = await run_video_generation_flow(image_input_local, prompt_input, cookies_file)
        print("\n--- 测试结果 ---")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    asyncio.run(main()) 