#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatGLM Video API GUI设置界面

功能：
- 图形化配置管理界面
- <PERSON>ie文件管理
- 服务器启动/停止控制
- 浏览器设置
- 实时状态显示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import subprocess
import os
import sys
import json
from typing import Optional
import requests
import time

# 添加必要的路径到Python路径
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
src_path = os.path.join(project_root, 'src')
tools_path = current_dir

# 确保路径在sys.path中
for path in [src_path, tools_path]:
    if path not in sys.path:
        sys.path.insert(0, path)

# 导入所需模块
from config_manager import ConfigManager  # type: ignore
from chatglm_cookies import ChatGLMCookieManager  # type: ignore

class ChatGLMSettingsGUI:
    """ChatGLM设置界面"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load_config()
        self.server_process: Optional[subprocess.Popen] = None
        self.cookie_manager = ChatGLMCookieManager()
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("ChatGLM Video API 设置管理器")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.setup_ui()
        self.load_current_config()
        self.update_status()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主要框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        
        # 创建标签页
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        self.main_frame.rowconfigure(0, weight=1)
        
        # 服务器设置标签页
        self.server_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.server_frame, text="服务器设置")
        self.setup_server_tab()
        
        # 浏览器设置标签页
        self.browser_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.browser_frame, text="浏览器设置")
        self.setup_browser_tab()
        
        # Cookie管理标签页
        self.cookie_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.cookie_frame, text="Cookie管理")
        self.setup_cookie_tab()
        
        # 日志标签页
        self.log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.log_frame, text="运行日志")
        self.setup_log_tab()
        
        # 底部控制按钮
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        self.setup_control_buttons()
        
        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        self.setup_status_bar()
    
    def setup_server_tab(self):
        """设置服务器配置标签页"""
        # 主机设置
        ttk.Label(self.server_frame, text="主机地址:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.host_var = tk.StringVar()
        self.host_entry = ttk.Entry(self.server_frame, textvariable=self.host_var, width=20)
        self.host_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 端口设置
        ttk.Label(self.server_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.port_var = tk.StringVar()
        self.port_entry = ttk.Entry(self.server_frame, textvariable=self.port_var, width=20)
        self.port_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 自动重载
        self.reload_var = tk.BooleanVar()
        self.reload_check = ttk.Checkbutton(self.server_frame, text="启用自动重载", variable=self.reload_var)
        self.reload_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 调试模式
        self.debug_var = tk.BooleanVar()
        self.debug_check = ttk.Checkbutton(self.server_frame, text="启用调试模式", variable=self.debug_var)
        self.debug_check.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 配置网格权重
        self.server_frame.columnconfigure(1, weight=1)
    
    def setup_browser_tab(self):
        """设置浏览器配置标签页"""
        # 浏览器模式
        ttk.Label(self.browser_frame, text="浏览器模式:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.headless_var = tk.BooleanVar()
        browser_mode_frame = ttk.Frame(self.browser_frame)
        browser_mode_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        ttk.Radiobutton(browser_mode_frame, text="有头模式（可见）", variable=self.headless_var, value=False).pack(side=tk.LEFT)
        ttk.Radiobutton(browser_mode_frame, text="无头模式（后台）", variable=self.headless_var, value=True).pack(side=tk.LEFT, padx=(20, 0))
        
        # 窗口宽度
        ttk.Label(self.browser_frame, text="窗口宽度:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.width_var = tk.StringVar()
        self.width_entry = ttk.Entry(self.browser_frame, textvariable=self.width_var, width=20)
        self.width_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 窗口高度
        ttk.Label(self.browser_frame, text="窗口高度:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.height_var = tk.StringVar()
        self.height_entry = ttk.Entry(self.browser_frame, textvariable=self.height_var, width=20)
        self.height_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 超时时间
        ttk.Label(self.browser_frame, text="超时时间(毫秒):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.timeout_var = tk.StringVar()
        self.timeout_entry = ttk.Entry(self.browser_frame, textvariable=self.timeout_var, width=20)
        self.timeout_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # User Agent
        ttk.Label(self.browser_frame, text="User Agent:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.user_agent_var = tk.StringVar()
        self.user_agent_entry = ttk.Entry(self.browser_frame, textvariable=self.user_agent_var, width=50)
        self.user_agent_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 配置网格权重
        self.browser_frame.columnconfigure(1, weight=1)
    
    def setup_cookie_tab(self):
        """设置Cookie管理标签页"""
        # Cookie文件路径
        ttk.Label(self.cookie_frame, text="Cookie文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        cookie_path_frame = ttk.Frame(self.cookie_frame)
        cookie_path_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        self.cookie_file_var = tk.StringVar()
        self.cookie_file_entry = ttk.Entry(cookie_path_frame, textvariable=self.cookie_file_var, width=40)
        self.cookie_file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.browse_button = ttk.Button(cookie_path_frame, text="浏览", command=self.browse_cookie_file)
        self.browse_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # Cookie操作按钮
        button_frame = ttk.Frame(self.cookie_frame)
        button_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        self.get_cookie_button = ttk.Button(button_frame, text="获取新Cookie", command=self.get_new_cookies)
        self.get_cookie_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.validate_cookie_button = ttk.Button(button_frame, text="验证Cookie", command=self.validate_cookies)
        self.validate_cookie_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.export_cookie_button = ttk.Button(button_frame, text="导出Cookie", command=self.export_cookies)
        self.export_cookie_button.pack(side=tk.LEFT)
        
        # Cookie状态显示
        self.cookie_status_frame = ttk.LabelFrame(self.cookie_frame, text="Cookie状态", padding="10")
        self.cookie_status_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(20, 0))
        
        self.cookie_status_text = scrolledtext.ScrolledText(self.cookie_status_frame, height=8, width=70)
        self.cookie_status_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置网格权重
        self.cookie_frame.columnconfigure(1, weight=1)
        self.cookie_frame.rowconfigure(2, weight=1)
    
    def setup_log_tab(self):
        """设置日志标签页"""
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=20, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(self.log_frame)
        log_button_frame.pack(fill=tk.X)
        
        self.clear_log_button = ttk.Button(log_button_frame, text="清空日志", command=self.clear_log)
        self.clear_log_button.pack(side=tk.LEFT)
        
        self.save_log_button = ttk.Button(log_button_frame, text="保存日志", command=self.save_log)
        self.save_log_button.pack(side=tk.LEFT, padx=(10, 0))
    
    def setup_control_buttons(self):
        """设置控制按钮"""
        # 配置按钮
        self.save_config_button = ttk.Button(self.control_frame, text="保存配置", command=self.save_config)
        self.save_config_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reset_config_button = ttk.Button(self.control_frame, text="重置配置", command=self.reset_config)
        self.reset_config_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 服务器控制按钮
        self.start_server_button = ttk.Button(self.control_frame, text="启动服务器", command=self.start_server)
        self.start_server_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.stop_server_button = ttk.Button(self.control_frame, text="停止服务器", command=self.stop_server)
        self.stop_server_button.pack(side=tk.RIGHT)
        self.stop_server_button.config(state='disabled')
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_label = ttk.Label(self.status_frame, text="就绪", relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.server_status_label = ttk.Label(self.status_frame, text="服务器状态: 停止", relief=tk.SUNKEN)
        self.server_status_label.pack(side=tk.RIGHT, padx=(10, 0))
    
    def load_current_config(self):
        """加载当前配置到界面"""
        # 服务器配置
        self.host_var.set(self.config.server.host)
        self.port_var.set(str(self.config.server.port))
        self.reload_var.set(self.config.server.reload)
        self.debug_var.set(self.config.debug)
        
        # 浏览器配置
        self.headless_var.set(self.config.browser.headless)
        self.width_var.set(str(self.config.browser.window_width))
        self.height_var.set(str(self.config.browser.window_height))
        self.timeout_var.set(str(self.config.browser.timeout))
        self.user_agent_var.set(self.config.browser.user_agent)
        
        # 路径配置
        self.cookie_file_var.set(self.config.paths.cookies_file)
    
    def save_config(self):
        """保存配置"""
        try:
            # 更新配置对象
            self.config.server.host = self.host_var.get()
            self.config.server.port = int(self.port_var.get())
            self.config.server.reload = self.reload_var.get()
            self.config.debug = self.debug_var.get()
            
            self.config.browser.headless = self.headless_var.get()
            self.config.browser.window_width = int(self.width_var.get())
            self.config.browser.window_height = int(self.height_var.get())
            self.config.browser.timeout = int(self.timeout_var.get())
            self.config.browser.user_agent = self.user_agent_var.get()
            
            self.config.paths.cookies_file = self.cookie_file_var.get()
            
            # 保存到文件
            if self.config_manager.save_config():
                self.log_message("✅ 配置保存成功")
                self.status_label.config(text="配置已保存")
            else:
                self.log_message("❌ 配置保存失败")
                
        except ValueError as e:
            messagebox.showerror("配置错误", f"配置值无效: {e}")
            self.log_message(f"❌ 配置保存失败: {e}")
    
    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认重置", "是否确定要重置为默认配置？"):
            self.config_manager.reset_to_defaults()
            self.config = self.config_manager.get_config()
            self.load_current_config()
            self.log_message("🔄 配置已重置为默认值")
            self.status_label.config(text="配置已重置")
    
    def browse_cookie_file(self):
        """浏览Cookie文件"""
        filename = filedialog.askopenfilename(
            title="选择Cookie文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            self.cookie_file_var.set(filename)
    
    def get_new_cookies(self):
        """获取新的Cookie"""
        self.log_message("🚀 开始获取Cookie...")
        self.get_cookie_button.config(state='disabled')
        
        def cookie_thread():
            try:
                if self.cookie_manager.scan_login():
                    cookie_file = self.config_manager.get_cookies_file_path()
                    if self.cookie_manager.save_cookies_to_file(cookie_file):
                        self.log_message("✅ Cookie获取并保存成功")
                        self.root.after(0, lambda: self.validate_cookies())
                    else:
                        self.log_message("❌ Cookie保存失败")
                else:
                    self.log_message("❌ Cookie获取失败")
            except Exception as e:
                self.log_message(f"❌ Cookie获取过程出错: {e}")
            finally:
                self.root.after(0, lambda: self.get_cookie_button.config(state='normal'))
        
        threading.Thread(target=cookie_thread, daemon=True).start()
    
    def validate_cookies(self):
        """验证Cookie有效性"""
        # 使用ConfigManager获取正确的Cookie文件路径
        cookie_file = self.config_manager.get_cookies_file_path()
        if not os.path.exists(cookie_file):
            self.cookie_status_text.delete(1.0, tk.END)
            self.cookie_status_text.insert(tk.END, f"❌ Cookie文件不存在: {cookie_file}\n")
            return
        
        self.log_message("🔍 验证Cookie有效性...")
        
        def validate_thread():
            try:
                if self.cookie_manager.load_cookies_from_file(cookie_file):
                    result = self.cookie_manager.validate_cookies()
                    
                    status_text = ""
                    if result['valid']:
                        status_text += "✅ Cookie验证成功\n"
                        if result['user_info']:
                            status_text += f"👤 用户信息: {result['user_info']}\n"
                        if result['expires_in'] and result['expires_in'] > 0:
                            days = int(result['expires_in'] // 86400)
                            hours = int((result['expires_in'] % 86400) // 3600)
                            status_text += f"⏰ Token剩余时间: {days}天 {hours}小时\n"
                    else:
                        status_text += "❌ Cookie验证失败\n"
                        if result['missing_tokens']:
                            status_text += f"缺少Token: {result['missing_tokens']}\n"
                        if result['expired_cookies']:
                            status_text += f"过期Cookie: {result['expired_cookies']}\n"
                    
                    self.root.after(0, lambda: self.update_cookie_status(status_text))
                else:
                    self.root.after(0, lambda: self.update_cookie_status("❌ Cookie文件加载失败\n"))
            except Exception as e:
                self.root.after(0, lambda: self.update_cookie_status(f"❌ 验证过程出错: {e}\n"))
        
        threading.Thread(target=validate_thread, daemon=True).start()
    
    def update_cookie_status(self, text: str):
        """更新Cookie状态显示"""
        self.cookie_status_text.delete(1.0, tk.END)
        self.cookie_status_text.insert(tk.END, text)
        self.log_message(text.strip())
    
    def export_cookies(self):
        """导出Cookie"""
        cookie_file = self.config_manager.get_cookies_file_path()
        if not os.path.exists(cookie_file):
            messagebox.showerror("错误", "Cookie文件不存在")
            return
        
        export_file = filedialog.asksaveasfilename(
            title="导出Cookie",
            defaultextension=".py",
            filetypes=[("Python文件", "*.py"), ("文本文件", "*.txt"), ("JSON文件", "*.json")]
        )
        
        if export_file:
            try:
                if self.cookie_manager.load_cookies_from_file(cookie_file):
                    file_ext = os.path.splitext(export_file)[1].lower()
                    format_map = {'.py': 'py', '.txt': 'txt', '.json': 'json'}
                    format_type = format_map.get(file_ext, 'json')
                    
                    if self.cookie_manager.export_cookies(format_type, export_file):
                        self.log_message(f"✅ Cookie导出成功: {export_file}")
                    else:
                        self.log_message("❌ Cookie导出失败")
                else:
                    self.log_message("❌ Cookie文件加载失败")
            except Exception as e:
                self.log_message(f"❌ 导出过程出错: {e}")
    
    def start_server(self):
        """启动服务器"""
        try:
            # 保存当前配置
            self.save_config()
            
            # 构建启动命令
            cmd = [
                sys.executable, "src/main.py"
            ]
            
            # 在新的进程中启动服务器
            # 确保从项目根目录启动
            project_root = os.path.dirname(os.path.dirname(__file__))
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=project_root
            )
            
            # 启动日志读取线程
            threading.Thread(target=self.read_server_output, daemon=True).start()
            
            # 更新界面状态
            self.start_server_button.config(state='disabled')
            self.stop_server_button.config(state='normal')
            self.server_status_label.config(text="服务器状态: 启动中...")
            
            self.log_message("🚀 服务器启动命令已发送...")
            
            # 延时检查服务器状态
            self.root.after(3000, self.check_server_status)
            
        except Exception as e:
            self.log_message(f"❌ 启动服务器失败: {e}")
            messagebox.showerror("启动失败", f"无法启动服务器: {e}")
    
    def stop_server(self):
        """停止服务器"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            except Exception as e:
                self.log_message(f"⚠️ 停止服务器时出错: {e}")
            
            self.server_process = None
        
        # 更新界面状态
        self.start_server_button.config(state='normal')
        self.stop_server_button.config(state='disabled')
        self.server_status_label.config(text="服务器状态: 已停止")
        self.log_message("🛑 服务器已停止")
    
    def read_server_output(self):
        """读取服务器输出"""
        if self.server_process and self.server_process.stdout:
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    self.root.after(0, lambda l=line: self.log_message(l.strip()))
                if self.server_process.poll() is not None:
                    break
        
        # 服务器进程结束
        if self.server_process:
            self.root.after(0, self.stop_server)
    
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = requests.get(f"http://{self.host_var.get()}:{self.port_var.get()}/", timeout=5)
            if response.status_code == 200:
                self.server_status_label.config(text="服务器状态: 运行中")
                self.log_message("✅ 服务器启动成功")
            else:
                self.server_status_label.config(text="服务器状态: 错误")
        except requests.RequestException:
            self.server_status_label.config(text="服务器状态: 未响应")
            # 再次检查
            self.root.after(2000, self.check_server_status)
    
    def update_status(self):
        """更新状态"""
        # 定期更新状态
        if self.server_process and self.server_process.poll() is None:
            # 服务器还在运行
            pass
        elif self.server_process and self.server_process.poll() is not None:
            # 服务器进程已结束
            self.stop_server()
        
        # 每5秒更新一次
        self.root.after(5000, self.update_status)
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = self.log_text.get(1.0, tk.END).count('\n')
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"📁 日志已保存: {filename}")
            except Exception as e:
                self.log_message(f"❌ 保存日志失败: {e}")
    
    def run(self):
        """运行GUI"""
        self.log_message("🎉 ChatGLM Video API 设置管理器已启动")
        self.log_message(f"📁 配置文件: {self.config_manager.config_file}")
        self.log_message(f"🍪 Cookie文件: {self.config_manager.get_cookies_file_path()}")
        
        # 验证Cookie
        self.validate_cookies()
        
        self.root.mainloop()
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.server_process:
            if messagebox.askyesno("确认退出", "服务器正在运行，是否确定退出？"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    """主函数"""
    app = ChatGLMSettingsGUI()
    app.root.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.run()

if __name__ == "__main__":
    main() 