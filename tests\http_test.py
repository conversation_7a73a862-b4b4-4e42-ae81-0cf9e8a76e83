#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatGLM 视频生成 API HTTP 请求示例 - Python版本

这个文件展示了如何使用纯HTTP请求调用ChatGLM视频生成API，
对应README.md中的curl命令示例。

功能特性：
- 基础调用示例（文本+图片）
- 高级调用示例（包含aspect_ratio、duration、ai_sound参数）
- 详细的请求和响应处理
- 错误处理和状态检查

使用方法：
python tests/http_test.py

依赖：
pip install requests
"""

import requests
import json
import time

# --- 配置 ---
API_URL = "http://127.0.0.1:8000/v1/chat/completions"
# 示例图片URL（来自README.md）
EXAMPLE_IMAGE_URL = "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/de0bdceb3b61410496d0016a5b355e73~tplv-tb4s082cfz-aigc_resize:2400:2400.webp?lk3s=43402efa&x-expires=1753488000&x-signature=v5Ze0ZPA2mrIPw2ThBKnD09yhcM%3D&format=.webp"
# 示例提示词
EXAMPLE_PROMPT = "让这只猫在月球上跳舞，电影感，史诗级"
# --- 配置结束 ---

def basic_http_request():
    """
    基础HTTP请求示例
    
    对应README.md中的基础调用示例：
    - 包含文本提示和图片URL
    - 使用默认参数
    """
    print("\n" + "="*60)
    print("🧪 基础HTTP请求示例")
    print("="*60)
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-your-fake-key"
    }
    
    # 请求体（对应README中的基础示例）
    payload = {
        "model": "glm-video",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": EXAMPLE_PROMPT
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": EXAMPLE_IMAGE_URL
                        }
                    }
                ]
            }
        ]
    }
    
    print(f"📝 请求URL: {API_URL}")
    print(f"📝 提示词: {EXAMPLE_PROMPT}")
    print(f"🖼️ 图片URL: {EXAMPLE_IMAGE_URL}")
    print(f"⚙️ 参数: 使用默认参数")
    
    try:
        print(f"\n🚀 发送HTTP请求...")
        response = requests.post(API_URL, headers=headers, json=payload, timeout=600)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ 请求成功")
            
            # 提取视频链接
            try:
                content = response_data["choices"][0]["message"]["content"]
                print(f"📄 响应内容: {content}")
                
                # 从Markdown链接中提取URL
                import re
                match = re.search(r'\((https://.*?)\)', content)
                if match:
                    video_url = match.group(1)
                    print(f"🎬 生成的视频链接: {video_url}")
                else:
                    print("⚠️ 未找到视频链接")
                    
            except (KeyError, IndexError) as e:
                print(f"❌ 响应格式错误: {e}")
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📄 错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"📄 原始响应: {response.text}")
                
    except requests.exceptions.Timeout:
        print("❌ 请求超时（600秒）")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务器正在运行")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def advanced_http_request():
    """
    高级HTTP请求示例
    
    对应README.md中的带参数的高级调用示例：
    - 包含文本提示和图片URL
    - 包含aspect_ratio、duration、ai_sound等参数
    """
    print("\n" + "="*60)
    print("🧪 高级HTTP请求示例（带参数）")
    print("="*60)
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-your-fake-key"
    }
    
    # 请求体（对应README中的高级示例）
    payload = {
        "model": "glm-video",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": EXAMPLE_PROMPT
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": EXAMPLE_IMAGE_URL
                        }
                    }
                ]
            }
        ],
        # 视频生成专用参数
        "aspect_ratio": "16:9",  # 生成比例
        "duration": "10s",       # 生成时长
        "ai_sound": True         # AI音效
    }
    
    print(f"📝 请求URL: {API_URL}")
    print(f"📝 提示词: {EXAMPLE_PROMPT}")
    print(f"🖼️ 图片URL: {EXAMPLE_IMAGE_URL}")
    print(f"📐 生成比例: {payload['aspect_ratio']}")
    print(f"⏱️ 生成时长: {payload['duration']}")
    print(f"🔊 AI音效: {'开启' if payload['ai_sound'] else '关闭'}")
    
    try:
        print(f"\n🚀 发送HTTP请求...")
        response = requests.post(API_URL, headers=headers, json=payload, timeout=600)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ 请求成功")
            
            # 提取视频链接
            try:
                content = response_data["choices"][0]["message"]["content"]
                print(f"📄 响应内容: {content}")
                
                # 从Markdown链接中提取URL
                import re
                match = re.search(r'\((https://.*?)\)', content)
                if match:
                    video_url = match.group(1)
                    print(f"🎬 生成的视频链接: {video_url}")
                else:
                    print("⚠️ 未找到视频链接")
                    
            except (KeyError, IndexError) as e:
                print(f"❌ 响应格式错误: {e}")
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📄 错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"📄 原始响应: {response.text}")
                
    except requests.exceptions.Timeout:
        print("❌ 请求超时（600秒）")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务器正在运行")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数：演示基础和高级HTTP请求"""
    print("="*60)
    print("ChatGLM 视频生成 API HTTP 请求示例 - Python版本")
    print("="*60)
    print(f"🕐 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 API地址: {API_URL}")
    print("\n📖 说明：")
    print("   这个脚本展示了如何使用Python的requests库")
    print("   发送HTTP请求到ChatGLM视频生成API")
    print("   对应README.md中的curl命令示例")
    
    # 执行基础请求示例
    basic_http_request()
    
    # 等待一段时间再执行高级请求
    print(f"\n⏳ 等待5秒后执行高级请求示例...")
    time.sleep(5)
    
    # 执行高级请求示例
    advanced_http_request()
    
    print("\n" + "="*60)
    print("🏁 HTTP请求示例演示完成")
    print("="*60)
    print("\n💡 提示：")
    print("   - 确保API服务器正在运行（uvicorn src.main:app --port 8000）")
    print("   - 确保cookies.json文件已正确配置")
    print("   - 视频生成可能需要较长时间，请耐心等待")

if __name__ == "__main__":
    main()
