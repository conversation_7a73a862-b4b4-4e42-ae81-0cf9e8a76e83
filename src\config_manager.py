#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatGLM Video API 配置管理器

功能：
- 管理服务器配置（端口、主机等）
- 管理浏览器配置（有头模式、窗口大小等）
- 管理Cookie文件路径
- 提供配置的加载、保存、验证功能
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8002
    reload: bool = True

@dataclass
class BrowserConfig:
    """浏览器配置"""
    headless: bool = False
    window_width: int = 1920
    window_height: int = 1080
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    timeout: int = 60000

@dataclass
class PathConfig:
    """路径配置"""
    cookies_file: str = "config/cookies.json"
    temp_dir: str = ""  # 空字符串表示使用系统临时目录

@dataclass
class AppConfig:
    """应用程序总配置"""
    server: ServerConfig
    browser: BrowserConfig
    paths: PathConfig
    debug: bool = False

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: str = "config/config.json"):
        self.config_file = config_file
        self._config: Optional[AppConfig] = None
        self.project_root = self._get_project_root()

    def _get_project_root(self) -> str:
        """获取项目根目录"""
        # 从当前文件位置向上查找项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # src/config_manager.py -> 项目根目录
        return os.path.dirname(current_dir)

    def _resolve_path(self, path: str) -> str:
        """解析路径，如果是相对路径则相对于项目根目录"""
        if os.path.isabs(path):
            return path
        return os.path.join(self.project_root, path)
    
    def get_default_config(self) -> AppConfig:
        """获取默认配置"""
        return AppConfig(
            server=ServerConfig(),
            browser=BrowserConfig(), 
            paths=PathConfig()
        )
    
    def load_config(self) -> AppConfig:
        """加载配置文件，如果不存在则创建默认配置"""
        config_file_path = self._resolve_path(self.config_file)
        if os.path.exists(config_file_path):
            try:
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 将字典转换为配置对象
                server_config = ServerConfig(**data.get('server', {}))
                browser_config = BrowserConfig(**data.get('browser', {}))
                paths_config = PathConfig(**data.get('paths', {}))
                
                self._config = AppConfig(
                    server=server_config,
                    browser=browser_config,
                    paths=paths_config,
                    debug=data.get('debug', False)
                )
                
                print(f"✅ 配置已加载: {self.config_file}")
                return self._config
                
            except Exception as e:
                print(f"⚠️ 加载配置失败: {e}")
                print("使用默认配置...")
                self._config = self.get_default_config()
        else:
            print(f"📄 配置文件不存在，创建默认配置: {self.config_file}")
            self._config = self.get_default_config()
            self.save_config()
        
        return self._config
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        if self._config is None:
            self._config = self.get_default_config()
        
        try:
            # 将配置对象转换为字典
            config_dict = {
                'server': asdict(self._config.server),
                'browser': asdict(self._config.browser),
                'paths': asdict(self._config.paths),
                'debug': self._config.debug
            }
            
            config_file_path = self._resolve_path(self.config_file)
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file_path), exist_ok=True)

            with open(config_file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)

            print(f"✅ 配置已保存: {config_file_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False
    
    def get_config(self) -> AppConfig:
        """获取当前配置"""
        if self._config is None:
            return self.load_config()
        return self._config
    
    def update_server_config(self, **kwargs) -> None:
        """更新服务器配置"""
        config = self.get_config()
        for key, value in kwargs.items():
            if hasattr(config.server, key):
                setattr(config.server, key, value)
    
    def update_browser_config(self, **kwargs) -> None:
        """更新浏览器配置"""
        config = self.get_config()
        for key, value in kwargs.items():
            if hasattr(config.browser, key):
                setattr(config.browser, key, value)
    
    def update_paths_config(self, **kwargs) -> None:
        """更新路径配置"""
        config = self.get_config()
        for key, value in kwargs.items():
            if hasattr(config.paths, key):
                setattr(config.paths, key, value)
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        config = self.get_config()
        issues = []
        
        # 验证端口范围
        if not (1 <= config.server.port <= 65535):
            issues.append(f"端口号无效: {config.server.port}")
        
        # 验证Cookie文件
        cookies_file_path = self._resolve_path(config.paths.cookies_file)
        if not os.path.exists(cookies_file_path):
            issues.append(f"Cookie文件不存在: {cookies_file_path}")
        
        # 验证窗口尺寸
        if config.browser.window_width <= 0 or config.browser.window_height <= 0:
            issues.append("浏览器窗口尺寸无效")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'config': config
        }
    
    def reset_to_defaults(self) -> None:
        """重置为默认配置"""
        self._config = self.get_default_config()
        self.save_config()

    def get_cookies_file_path(self) -> str:
        """获取Cookie文件的绝对路径"""
        config = self.get_config()
        return self._resolve_path(config.paths.cookies_file)

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config() -> AppConfig:
    """获取全局配置的便捷函数"""
    return config_manager.get_config()

if __name__ == "__main__":
    # 测试配置管理器
    print("=" * 50)
    print("配置管理器测试")
    print("=" * 50)
    
    # 加载配置
    config = config_manager.load_config()
    print(f"服务器端口: {config.server.port}")
    print(f"浏览器模式: {'有头' if not config.browser.headless else '无头'}")
    print(f"Cookie文件: {config.paths.cookies_file}")
    
    # 验证配置
    validation = config_manager.validate_config()
    if validation['valid']:
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败:")
        for issue in validation['issues']:
            print(f"  - {issue}") 