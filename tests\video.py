import requests
import json
import base64
import os
import argparse
import time
import re

# --- 配置 ---
API_URL = "http://localhost:8000/v1/chat/completions"
LOCAL_IMAGE_PATH = "config/assets/1.jpg"
REMOTE_IMAGE_URL = "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/de0bdceb3b61410496d0016a5b355e73~tplv-tb4s082cfz-aigc_resize:2400:2400.webp?lk3s=43402efa&x-expires=1753488000&x-signature=v5Ze0ZPA2mrIPw2ThBKnD09yhcM%3D&format=.webp"
DEFAULT_PROMPT = "让这只猫在月球上跳舞，电影感，史诗级"
# --- 配置结束 ---

def encode_image_to_base64(image_path):
    """将本地图片编码为Base64字符串"""
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"❌ 文件不存在: {image_path}")
            return None
            
        # 读取并编码
        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            return base64_data
            
    except Exception as e:
        print(f"❌ 图片编码失败: {e}")
        return None

def test_api(use_local=True, custom_prompt=None, aspect_ratio=None, duration="5s", ai_sound=False):
    """测试API调用"""
    prompt = custom_prompt or DEFAULT_PROMPT
    
    print(f"\n{'='*60}")
    test_type = "本地图片" if use_local else "网络图片"
    print(f"🧪 开始测试: {test_type}")
    print(f"{'='*60}")
    
    if use_local:
        print(f"🖼️ 测试本地图片: {LOCAL_IMAGE_PATH}")
        
        # 检查图片文件目录
        assets_dir = "config/assets"
        if os.path.exists(assets_dir):
            image_files = [f for f in os.listdir(assets_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp'))]
            print(f"📋 在 {assets_dir} 目录找到 {len(image_files)} 个图片文件")
        else:
            print(f"📋 图片目录 {assets_dir} 不存在")
        
        if not os.path.exists(LOCAL_IMAGE_PATH):
            print(f"❌ 本地图片不存在: {LOCAL_IMAGE_PATH}")
            return False
        
        base64_image = encode_image_to_base64(LOCAL_IMAGE_PATH)
        if not base64_image:
            print("❌ 图片编码失败，测试终止")
            return False
        
        image_content = {
            "type": "image_url",
            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
        }
        
    else:
        print(f"🌐 测试网络图片: {REMOTE_IMAGE_URL}")
        image_content = {
            "type": "image_url",
            "image_url": {"url": REMOTE_IMAGE_URL}
        }
    
    # 构建请求
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-test-key"
    }
    
    payload = {
        "model": "glm-video",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    image_content
                ]
            }
        ]
    }

    # 添加新的视频生成参数
    if aspect_ratio:
        payload["aspect_ratio"] = aspect_ratio
    if duration != "5s":  # 只有非默认值才添加
        payload["duration"] = duration
    if ai_sound:  # 只有开启时才添加
        payload["ai_sound"] = ai_sound
    
    print(f"\n🚀 发送请求...")
    print(f"📝 提示词: {prompt}")
    if aspect_ratio:
        print(f"📐 生成比例: {aspect_ratio}")
    print(f"⏱️ 生成时长: {duration}")
    print(f"🔊 AI音效: {'开启' if ai_sound else '关闭'}")
    
    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=600)
        print(f"📊 状态码: {response.status_code}")
        
        # 检查HTTP状态码
        if response.status_code != 200:
            print(f"❌ API返回错误状态码: {response.status_code}")
            try:
                error_json = response.json()
                print("📄 错误详情:")
                print(json.dumps(error_json, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print("📄 原始错误响应:")
                print(response.text)
            return False
        
        try:
            response_json = response.json()
            print("✅ 收到响应")
            
            # 检查并打印视频链接
            try:
                content = response_json["choices"][0]["message"]["content"]
                # 从Markdown链接中提取URL: [text](url)
                match = re.search(r'\((https://.*?)\)', content)
                if match:
                    video_url = match.group(1)
                    print(f"🎬 视频链接: {video_url}")
                    return True
                else:
                    print("⚠️ 响应中未找到有效的视频链接。")
                    print(f"   > 响应内容: {content}")
                    return False
            except (KeyError, IndexError, TypeError):
                print("⚠️ 响应格式异常，无法提取视频信息。")
                return False
                
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON格式")
            print("📄 原始响应:")
            print(response.text)
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时（600秒）")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务器正在运行")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False

def main():
    global API_URL
    
    parser = argparse.ArgumentParser(description='ChatGLM视频生成API测试工具')
    parser.add_argument('--mode', choices=['local', 'remote', 'both'], default='both',
                       help='测试模式: local(本地图片), remote(网络图片), both(两种都测试)')
    parser.add_argument('--prompt', type=str, help='自定义提示词')
    parser.add_argument('--url', type=str, default=API_URL, help='API服务器地址')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细日志')
    # 新增视频生成参数
    parser.add_argument('--aspect-ratio', choices=['16:9', '9:16', '1:1', '4:3', '3:4'],
                       help='生成比例 (仅文生视频模式)')
    parser.add_argument('--duration', choices=['5s', '10s'], default='5s',
                       help='生成时长')
    parser.add_argument('--ai-sound', action='store_true',
                       help='开启AI音效')
    
    args = parser.parse_args()
    API_URL = args.url
    
    print("=" * 60)
    print("ChatGLM 视频生成 API 测试工具")
    print("=" * 60)
    print(f"🌐 API地址: {API_URL}")
    print(f"📝 提示词: {args.prompt or DEFAULT_PROMPT}")
    print(f"� 生成比例: {args.aspect_ratio or '默认'}")
    print(f"⏱️ 生成时长: {args.duration}")
    print(f"🔊 AI音效: {'开启' if args.ai_sound else '关闭'}")
    print(f"�🕐 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    success_count = 0
    total_tests = 0
    
    if args.mode in ['local', 'both']:
        if test_api(use_local=True, custom_prompt=args.prompt,
                   aspect_ratio=args.aspect_ratio, duration=args.duration, ai_sound=args.ai_sound):
            success_count += 1
        total_tests += 1

    if args.mode in ['remote', 'both']:
        if test_api(use_local=False, custom_prompt=args.prompt,
                   aspect_ratio=args.aspect_ratio, duration=args.duration, ai_sound=args.ai_sound):
            success_count += 1
        total_tests += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {success_count}/{total_tests} 成功")
    if success_count == total_tests:
        print("🎉 所有测试都通过了！")
    elif success_count > 0:
        print("⚠️ 部分测试失败，请检查上面的错误信息")
    else:
        print("❌ 所有测试都失败了，请检查服务器状态和配置")
    print("=" * 60)

if __name__ == "__main__":
    main() 